"""
User Story Generation Module
Generates user stories using document content + FAQ answers
"""

import logging
from typing import List
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

class JiraIssue(BaseModel):
    title: str = Field(description="The title of the Jira issue.")
    description: str = Field(description="Detailed description of the Jira issue.")
    acceptance_criteria: str = Field(description="Acceptance criteria for the Jira issue.")
    priority: str = Field(description="Priority level of the Jira issue (e.g., High, Medium, Low).")
    story_points: int = Field(description="Story points assigned to the Jira issue for estimation.")

class JiraEpic(BaseModel):
    title: str = Field(description="The title of the Jira epic.")
    description: str = Field(description="Detailed description of the Jira epic.")
    issues: List[JiraIssue] = Field(description="List of issues associated with this epic.")

class UserStoryGenerator:
    """Generates user stories using document content + FAQ answers"""
    
    def __init__(self, llm_model):
        """
        Initialize user story generator
        
        Args:
            llm_model: Lang<PERSON>hain LLM model instance
        """
        if not llm_model:
            raise ValueError("LLM model not available")
        self.model = llm_model
        
        # Enhanced prompt for user story generation
        self.story_prompt = """
You are an expert Business Analyst tasked with generating comprehensive Agile Scrum User Stories from a Business Requirements Document (BRD) and related FAQ answers.

You have been provided with:
1. The complete BRD content
2. FAQ answers that provide additional context and clarifications

Your task is to generate a JIRA epic and a comprehensive list of Agile Scrum User Stories that cover all the requirements mentioned in both the BRD and the FAQ answers.

Important guidelines:
- Analyze both the BRD content and FAQ answers to understand the complete scope
- Generate user stories that are INVEST compliant (Independent, Negotiable, Valuable, Estimable, Small, Testable)
- Include acceptance criteria in Given/When/Then format
- Assign appropriate priority levels (High/Medium/Low)
- Assign story points using Fibonacci scale (1, 2, 3, 5, 8, 13)
- Focus on user value and business outcomes
- Avoid technical implementation details
- Consider container depot management, yard operations, and tracking systems

Structure for each story:
1. Title (clear and concise)
2. Description (As a [user], I want [goal] so that [benefit])
3. Acceptance Criteria (Given/When/Then format)
4. Priority (High/Medium/Low)
5. Story Points (1, 2, 3, 5, 8, 13)

Guidelines:
- Follow the INVEST principle (Independent, Negotiable, Valuable, Estimable, Small, Testable)
- Avoid technical or implementation specifics
- Extract only actionable, testable features and behaviors
- Consider the context is for a Scrum team working in 2-week sprints
- The product feature set is intended to enhance user experience for Container Depots

BRD Content:
{brd_content}

FAQ Answers and Additional Context:
{faq_context}

Generate a comprehensive epic with user stories that address all the requirements and clarifications provided.
"""
    
    def generate_user_stories(self, document_text: str, faq_answers: List) -> JiraEpic:
        """
        Generate user stories from document and FAQ answers
        
        Args:
            document_text: The BRD text content
            faq_answers: List of FAQAnswer objects
            
        Returns:
            JiraEpic object with generated user stories
        """
        
        # Prepare FAQ context
        faq_context = "\n\n".join([
            f"Q: {faq.question}\nA: {faq.answer}\nRelevance Score: {faq.score:.2f}"
            for faq in faq_answers
        ])
        
        # Limit content to avoid token limits
        limited_brd = document_text[:6000] if len(document_text) > 6000 else document_text
        limited_faq = faq_context[:4000] if len(faq_context) > 4000 else faq_context
        
        # Combine contexts
        full_context = self.story_prompt.format(
            brd_content=limited_brd,
            faq_context=limited_faq
        )
        
        try:
            # Use structured output for consistent format
            structured_llm = self.model.with_structured_output(JiraEpic)
            epic = structured_llm.invoke(full_context)
            
            logger.info(f"Generated epic '{epic.title}' with {len(epic.issues)} user stories")
            return epic
            
        except Exception as e:
            logger.error(f"Error generating user stories: {e}")
            # Return a fallback epic
            return self._get_fallback_epic(document_text, faq_answers)
    
    def _get_fallback_epic(self, document_text: str, faq_answers: List) -> JiraEpic:
        """
        Generate a fallback epic if the main generation fails
        
        Args:
            document_text: The BRD text content
            faq_answers: List of FAQAnswer objects
            
        Returns:
            Fallback JiraEpic object
        """
        return JiraEpic(
            title="Container Depot Management Enhancement",
            description=f"Epic for implementing container depot management features based on BRD analysis. Document contains {len(document_text)} characters of requirements and {len(faq_answers)} FAQ clarifications.",
            issues=[
                JiraIssue(
                    title="Container Tracking System",
                    description="As a depot manager, I want to track container locations in real-time so that I can efficiently manage container movements and reduce search time",
                    acceptance_criteria="Given I am logged into the system, When I search for a container ID, Then I can see its current location within 5 minutes accuracy",
                    priority="High",
                    story_points=8
                ),
                JiraIssue(
                    title="Yard Management Dashboard",
                    description="As an operations manager, I want a comprehensive yard management dashboard so that I can monitor overall depot operations",
                    acceptance_criteria="Given I access the dashboard, When I view the yard status, Then I can see container counts, available spaces, and operational metrics",
                    priority="High",
                    story_points=5
                ),
                JiraIssue(
                    title="Automated Reporting System",
                    description="As a depot supervisor, I want automated daily reports so that I can track performance metrics and identify issues",
                    acceptance_criteria="Given it's 6 AM daily, When the system generates reports, Then reports are available in my dashboard with key performance indicators",
                    priority="Medium",
                    story_points=5
                ),
                JiraIssue(
                    title="Mobile Access for Field Workers",
                    description="As a field worker, I want mobile access to container information so that I can update container status while working in the yard",
                    acceptance_criteria="Given I have the mobile app, When I scan a container, Then I can view and update its status even when offline",
                    priority="Medium",
                    story_points=8
                ),
                JiraIssue(
                    title="Integration with External Systems",
                    description="As a system administrator, I want seamless integration with external systems so that data flows automatically between platforms",
                    acceptance_criteria="Given external system integration is configured, When data changes in one system, Then it's automatically synchronized with connected systems",
                    priority="High",
                    story_points=13
                )
            ]
        )
