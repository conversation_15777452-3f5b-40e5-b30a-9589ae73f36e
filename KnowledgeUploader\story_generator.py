"""
User Story Generation Module
Generates user stories using document content + FAQ answers
"""

import logging
from typing import List
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

class JiraIssue(BaseModel):
    title: str = Field(description="The title of the Jira issue.")
    description: str = Field(description="Detailed description of the Jira issue.")
    acceptance_criteria: str = Field(description="Acceptance criteria for the Jira issue.")
    priority: str = Field(description="Priority level of the Jira issue (e.g., High, Medium, Low).")
    story_points: int = Field(description="Story points assigned to the Jira issue for estimation.")

class JiraEpic(BaseModel):
    title: str = Field(description="The title of the Jira epic.")
    description: str = Field(description="Detailed description of the Jira epic.")
    issues: List[JiraIssue] = Field(description="List of issues associated with this epic.")

class UserStoryGenerator:
    """Generates user stories using document content + FAQ answers"""
    
    def __init__(self, llm_model):
        """
        Initialize user story generator
        
        Args:
            llm_model: Lang<PERSON>hain <PERSON>M model instance
        """
        if not llm_model:
            raise ValueError("LLM model not available")
        self.model = llm_model
        

        self.story_prompt = """
            You are an expert Business Analyst. Generate a JIRA epic with user stories from the BRD and FAQ context provided.

            Requirements:
            - Create 1 epic with 5-8 focused user stories
            - Each story: Title, Description (As a [user], I want [goal] so that [benefit]), Acceptance Criteria (Given/When/Then), Priority (High/Medium/Low), Story Points (1,2,3,5,8,13)
            - INVEST principle: Independent, Negotiable, Valuable, Estimable, Small, Testable
            - Prioritize high-value features for 2-week sprints

            BRD Content:
            {brd_content}

            FAQ Context:
            {faq_context}

            Generate a comprehensive epic addressing the key requirements from both sources.
        """
    
    def generate_user_stories(self, document_text: str, faq_answers: List) -> JiraEpic:
        """
        Generate user stories from document and FAQ answers

        Args:
            document_text: The BRD text content
            faq_answers: List of FAQAnswer objects

        Returns:
            JiraEpic object with generated user stories
        """

        faq_context = "\n\n".join([
            f"Q: {faq.question}\nA: {faq.answer}\nRelevance Score: {faq.score:.2f}"
            for faq in faq_answers
        ])

        
        brd_tokens = len(document_text) // 4
        faq_tokens = len(faq_context) // 4
        total_input_tokens = brd_tokens + faq_tokens

        logger.info(f"Token estimation - BRD: {brd_tokens}, FAQ: {faq_tokens}, Total input: {total_input_tokens}")

        if total_input_tokens <= 25000:  
            final_brd = document_text
            final_faq = faq_context
            logger.info("Using full BRD and FAQ content")
        else:
            max_faq_chars = min(len(faq_context), 8000) 
            final_brd = document_text
            final_faq = faq_context[:max_faq_chars] if len(faq_context) > max_faq_chars else faq_context
            logger.info(f"Using full BRD ({len(final_brd)} chars) and limited FAQ ({len(final_faq)} chars)")

        full_context = self.story_prompt.format(
            brd_content=final_brd,
            faq_context=final_faq
        )

        try:
            structured_llm = self.model.with_structured_output(JiraEpic)
            epic = structured_llm.invoke(full_context)

            logger.info(f"Generated epic '{epic.title}' with {len(epic.issues)} user stories")
            return epic

        except Exception as e:
            logger.error(f"Error generating user stories: {e}")
    
    
