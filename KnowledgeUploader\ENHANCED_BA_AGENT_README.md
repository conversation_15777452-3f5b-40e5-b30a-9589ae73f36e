# Enhanced BA Agent API v2.0

## 🎯 Overview

The Enhanced BA Agent is a comprehensive system that processes Business Requirements Documents (BRDs) and generates user stories by:

1. **📤 Uploading & Processing DOCX files** - Extracts text content from uploaded documents
2. **❓ Generating Questions** - Uses LLM to create relevant questions based on the document
3. **🔍 FAQ Search** - Searches vector database for answers to generated questions
4. **📝 User Story Generation** - Combines document content + FAQ answers to create comprehensive user stories

## 🚀 Quick Start

### 1. Install Dependencies
```bash
cd KnowledgeUploader
pip install -r requirements.txt
```

### 2. Set Environment Variables
Ensure your `.env` file contains:
```env
# Azure OpenAI Configuration
AZURE_OPENAI_ENDPOINT_4_1=your_endpoint
AZURE_OPENAI_API_KEY_4_1=your_api_key
AZURE_OPENAI_VERSION_4_1=2025-01-01-preview

# Embedding Model Configuration
EMBEDDING_OPENAI_API_BASE=your_embedding_endpoint
EMBEDDING_OPENAI_API_KEY=your_embedding_api_key
EMBEDDING_OPENAI_API_VERSION=2023-05-15

# Qdrant Configuration
QDRANT_ENDPOINT=your_qdrant_url
QDRANT_API_KEY=your_qdrant_api_key
QDRANT_BRD_COLLECTION_NAME=your_collection_name
```

### 3. Start the Server
```bash
python BA_Agent.py server
```

The API will be available at: `http://localhost:8000`

## 📡 API Endpoints

### Core Workflow Endpoints

#### 1. Upload Document
```http
POST /upload-document
Content-Type: multipart/form-data

Body: file (DOCX format)
```

**Response:**
```json
{
  "success": true,
  "message": "Document uploaded and processed successfully",
  "document_id": "uuid-string",
  "filename": "your-document.docx",
  "text_length": 15420
}
```

#### 2. Generate Questions
```http
POST /generate-questions/{document_id}
```

**Response:**
```json
{
  "success": true,
  "document_id": "uuid-string",
  "questions": [
    {
      "question": "What are the specific user authentication requirements?",
      "domain": "Security",
      "priority": "High"
    }
  ],
  "questions_count": 12
}
```

#### 3. Search FAQ Answers
```http
POST /search-faq/{document_id}
Content-Type: application/json

{
  "document_id": "uuid-string",
  "collection_name": "optional-collection-name"
}
```

**Response:**
```json
{
  "success": true,
  "document_id": "uuid-string",
  "faq_answers": [
    {
      "question": "What are the authentication requirements?",
      "answer": "The system requires multi-factor authentication...",
      "score": 0.85,
      "source_document": "faq_document.docx"
    }
  ],
  "answers_found": 8
}
```

#### 4. Generate User Stories
```http
POST /generate-user-stories/{document_id}
```

**Response:**
```json
{
  "success": true,
  "document_id": "uuid-string",
  "epic": {
    "title": "Container Management System Enhancement",
    "description": "Epic for implementing container tracking and management features",
    "issues": [
      {
        "title": "User Authentication System",
        "description": "As a user, I want to securely log into the system so that my data is protected",
        "acceptance_criteria": "Given a valid user, When they enter credentials, Then they are authenticated",
        "priority": "High",
        "story_points": 5
      }
    ]
  },
  "questions_count": 12,
  "faq_answers_count": 8
}
```

### Convenience Endpoints

#### Complete Workflow (All-in-One)
```http
POST /process-brd-complete
Content-Type: multipart/form-data

Body: file (DOCX format)
Optional: collection_name (form field)
```

This endpoint runs the entire workflow in one call: Upload → Questions → FAQ Search → User Stories

#### Document Management
```http
GET /processed-documents          # List all processed documents
GET /document/{document_id}       # Get document details
GET /health                       # Health check
GET /collections                  # List Qdrant collections
```

## 🧪 Testing

### Run Test Suite
```bash
python test_enhanced_ba_agent.py
```

### Manual Testing with Bruno

#### 1. Upload Document
- **Method:** POST
- **URL:** `http://localhost:8000/upload-document`
- **Body:** Form-data with `file` field (select your DOCX file)

#### 2. Generate Questions
- **Method:** POST  
- **URL:** `http://localhost:8000/generate-questions/{document_id}`
- **Replace** `{document_id}` with the ID from step 1

#### 3. Search FAQ
- **Method:** POST
- **URL:** `http://localhost:8000/search-faq/{document_id}`
- **Headers:** `Content-Type: application/json`
- **Body:**
```json
{
  "document_id": "your-document-id-here"
}
```

#### 4. Generate User Stories
- **Method:** POST
- **URL:** `http://localhost:8000/generate-user-stories/{document_id}`

#### 5. Complete Workflow (Alternative)
- **Method:** POST
- **URL:** `http://localhost:8000/process-brd-complete`
- **Body:** Form-data with `file` field

## 🔧 Architecture

### Components

1. **DocumentProcessor** - Extracts text from DOCX files
2. **QuestionGenerator** - Uses LLM to generate relevant questions
3. **FAQSearcher** - Searches vector database using embeddings
4. **EnhancedUserStoryGenerator** - Creates user stories from combined context
5. **QdrantHTTPClient** - Handles vector database operations

### Data Flow

```
DOCX Upload → Text Extraction → Question Generation → FAQ Search → User Story Generation
     ↓              ↓                    ↓              ↓              ↓
  File Storage → Document Store → Question Store → FAQ Store → Epic Store
```

### Payload Structure Expected

Your Qdrant collection should have this payload structure:
```json
{
  "content": "The actual text content",
  "filename": "source_document.docx",
  "document_id": "uuid",
  "document_type": "BRD",
  "chunk_index": 1,
  "metadata": {
    "timestamp": "2025-07-10T21:32:52.266278",
    "total_chunks": 27
  }
}
```

## 🎯 Key Features

- ✅ **DOCX Processing** - Automatic text extraction from Word documents
- ✅ **Intelligent Question Generation** - LLM creates relevant questions
- ✅ **Vector Search Integration** - Finds FAQ answers using embeddings
- ✅ **Enhanced User Stories** - Combines document + FAQ context
- ✅ **Complete Workflow** - Single endpoint for full process
- ✅ **Document Management** - Track processing status
- ✅ **Error Handling** - Comprehensive error responses
- ✅ **API Documentation** - Auto-generated docs at `/docs`

## 🔍 Troubleshooting

### Common Issues

1. **"LLM model not available"**
   - Check Azure OpenAI credentials in `.env`
   - Verify deployment name matches your Azure setup

2. **"Cannot connect to Qdrant"**
   - Verify Qdrant endpoint and API key
   - Check collection name exists

3. **"No FAQ answers found"**
   - Verify your collection has content
   - Check embedding model configuration
   - Lower score threshold in FAQ search

4. **"Document upload failed"**
   - Ensure file is in DOCX format
   - Check file size limits
   - Verify python-docx is installed

### Debug Mode

Add logging to see detailed processing:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📚 API Documentation

Interactive API documentation is available at:
- **Swagger UI:** `http://localhost:8000/docs`
- **ReDoc:** `http://localhost:8000/redoc`

## 🎉 Success!

Your Enhanced BA Agent is now ready to process BRDs and generate comprehensive user stories with FAQ integration! 🚀
