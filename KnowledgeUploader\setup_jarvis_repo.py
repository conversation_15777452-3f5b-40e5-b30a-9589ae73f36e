#!/usr/bin/env python3
"""
Setup script to prepare Enhanced BA Agent files for Jarvis Knowledge repository
"""

import os
import shutil
from pathlib import Path

def setup_jarvis_repo():
    """Setup the Enhanced BA Agent in Jarvis Knowledge repository structure"""
    
    print("🚀 Setting up Enhanced BA Agent for Jarvis Knowledge Repository")
    print("=" * 60)
    
    # Define source and target paths
    current_dir = Path(__file__).parent
    
    # Files to copy to the new repository
    files_to_copy = {
        # Main application files
        "main.py": "main.py",
        "requirements.txt": "requirements.txt",
        
        # Agent modules
        "agents/BA_Agent.py": "agents/ba_agent.py",
        "agents/__init__.py": "agents/__init__.py",
        
        # Utility modules
        "utils/brd_processor.py": "utils/brd_processor.py",
        "utils/question_generator.py": "utils/question_generator.py", 
        "utils/faq_searcher.py": "utils/faq_searcher.py",
        "utils/story_generator.py": "utils/story_generator.py",
        "utils/__init__.py": "utils/__init__.py",
    }
    
    # Create directory structure
    directories = [
        "agents",
        "utils", 
        "docs",
        "tests",
        "config"
    ]
    
    print("📁 Files to copy to your Jarvis Knowledge repository:")
    print()
    
    for src, dst in files_to_copy.items():
        src_path = current_dir / src
        if src_path.exists():
            print(f"✅ {src} → {dst}")
        else:
            print(f"❌ {src} (not found)")
    
    print()
    print("📂 Directory structure to create:")
    for directory in directories:
        print(f"📁 {directory}/")
    
    print()
    print("🔧 Next steps:")
    print("1. Clone your Jarvis Knowledge repository:")
    print("   git clone https://github.com/Maersk-Global/jarvis-knowledge.git")
    print("   cd jarvis-knowledge")
    print()
    print("2. Create the directory structure:")
    for directory in directories:
        print(f"   mkdir -p {directory}")
    print()
    print("3. Copy the files from your current project:")
    for src, dst in files_to_copy.items():
        print(f"   cp KnowledgeUploader/{src} jarvis-knowledge/{dst}")
    print()
    print("4. Copy additional files:")
    print("   cp KnowledgeUploader/.gitignore jarvis-knowledge/")
    print("   cp KnowledgeUploader/README.md jarvis-knowledge/")
    print()
    print("5. Initialize and push:")
    print("   git add .")
    print("   git commit -m 'Initial commit: Enhanced BA Agent with modular architecture'")
    print("   git push origin main")

if __name__ == "__main__":
    setup_jarvis_repo()
