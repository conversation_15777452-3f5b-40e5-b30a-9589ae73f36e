@echo off
REM Enhanced BA Agent API Startup Script for Windows

echo Starting Enhanced BA Agent API...

REM Check if virtual environment exists
if not exist ".venv" (
    echo Creating virtual environment...
    python -m venv .venv
)

REM Activate virtual environment
echo Activating virtual environment...
call .venv\Scripts\activate.bat

REM Install dependencies
echo Installing dependencies...
pip install -r requirements.txt

REM Check if .env file exists
if not exist "..\\.env" (
    echo Error: .env file not found in parent directory!
    echo Please ensure the .env file exists with the required configuration.
    pause
    exit /b 1
)

echo Starting Enhanced BA Agent API server...
echo API will be available at: http://localhost:8000
echo Main endpoint: POST http://localhost:8000/process-brd
echo Health check: GET http://localhost:8000/health
echo API documentation: http://localhost:8000/docs
echo.

python BA_Agent.py

pause
