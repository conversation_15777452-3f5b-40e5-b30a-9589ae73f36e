"""
Question Generation Module
Generates questions from BRD content using LLM
"""

import json
import logging
from typing import List
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

class GeneratedQuestion(BaseModel):
    question: str
    domain: str

class QuestionGenerator:
    """Generates questions from document content using LLM"""
    
    def __init__(self, llm_model):
        """
        Initialize question generator
        
        Args:
            llm_model: LangChain LLM model instance
        """
        if not llm_model:
            raise ValueError("LLM model not available")
        self.model = llm_model
    
    def generate_questions(self, document_text: str) -> List[GeneratedQuestion]:
        """
        Generate relevant questions from document text
        
        Args:
            document_text: The BRD text content
            
        Returns:
            List of GeneratedQuestion objects
        """
        
        question_prompt = """
You are an expert Business Analyst specializing in container depot management and logistics systems. You have deep knowledge of container operations, yard management, and maritime logistics terminology.

CONTEXT: This BRD relates to container depot operations and may include specialized terms such as:
- Reefer operations: Management of refrigerated containers with temperature control
- Yard management: Container positioning, tracking, and movement within depot facilities
- Bonded operations: Customs-controlled container handling and storage
- Container handshake: Data exchange and coordination between systems/stakeholders
- Landside operations: Ground-based container handling (trucks, rail, yard equipment)
- Vessel operations: Ship-based container loading/unloading
- Terminal operations: Port and depot container management
- Setpoint management: Temperature control settings for reefer containers
- Claims management: Handling of damage, loss, or temperature excursion claims
- Transshipment: Container transfer between different transport modes
- OHP (Operational Handover Process): Systematic transfer of operational responsibility

Analyze the following Business Requirements Document (BRD) and generate 10-15 specific, detailed questions that would help understand the requirements better, keeping in mind the container depot and logistics context.

Focus on generating questions about:
- Functional requirements and features specific to container operations
- User interface and user experience for depot workers, managers, and stakeholders
- System integrations between depot systems, shipping lines, customs, and logistics partners
- Business processes and workflows for container handling, tracking, and documentation
- Data management and reporting for operational metrics, compliance, and performance
- Security and compliance requirements for customs, cargo, and operational safety
- Performance and scalability needs for high-volume container operations
- Container management including reefer operations, bonded storage, and yard positioning
- Tracking and monitoring capabilities for containers, equipment, and operational status
- Temperature management and monitoring for reefer containers
- Stakeholder coordination and communication workflows
- Integration with external systems (customs, shipping lines, trucking companies)

For each question, suggest a relevant domain/category such as: "Reefer Operations", "Yard Management", "Integration", "Security", "Reporting", "Container Tracking", "Temperature Management", "Bonded Operations", "UI/UX", "Performance", "Compliance", "Stakeholder Management", etc.

Return the response as a JSON array with this exact format:
[
  {
    "question": "What are the specific reefer setpoint management requirements and how should temperature changes be communicated to stakeholders?",
    "domain": "Reefer Operations"
  },
  {
    "question": "How should the system handle bonded container operations and customs compliance workflows?",
    "domain": "Bonded Operations"
  }
]

IMPORTANT: Return ONLY the JSON array, no other text or formatting.

Document Content:
{document_text}
"""
        
        try:

            limited_text = document_text[:20000] if len(document_text) > 20000 else document_text
            logger.info(f"Using {len(limited_text)} characters of {len(document_text)} total for question generation")
            
            response = self.model.invoke(question_prompt.format(document_text=limited_text))
            
            response_content = response.content if hasattr(response, 'content') else str(response)
            
            response_content = response_content.strip()
            if response_content.startswith('```json'):
                response_content = response_content[7:]
            if response_content.endswith('```'):
                response_content = response_content[:-3]
            response_content = response_content.strip()
            
            questions_data = json.loads(response_content)
            
            questions = []
            for q_data in questions_data:
                if isinstance(q_data, dict) and 'question' in q_data:
                    questions.append(GeneratedQuestion(
                        question=q_data.get("question", ""),
                        domain=q_data.get("domain", "General")
                    ))
            
            logger.info(f"Generated {len(questions)} questions from document")
            return questions
            
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing JSON response: {e}")
            logger.error(f"Response content: {response_content}")
    
            return self._get_fallback_questions()
            
        except Exception as e:
            logger.error(f"Error generating questions: {e}")

