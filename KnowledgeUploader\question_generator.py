"""
Question Generation Module
Generates questions from BRD content using LLM
"""

import json
import logging
from typing import List
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

class GeneratedQuestion(BaseModel):
    question: str
    domain: str

class QuestionGenerator:
    """Generates questions from document content using LLM"""
    
    def __init__(self, llm_model):
        """
        Initialize question generator
        
        Args:
            llm_model: LangChain LLM model instance
        """
        if not llm_model:
            raise ValueError("LLM model not available")
        self.model = llm_model
    
    def generate_questions(self, document_text: str) -> List[GeneratedQuestion]:
        """
        Generate relevant questions from document text
        
        Args:
            document_text: The BRD text content
            
        Returns:
            List of GeneratedQuestion objects
        """
        
        question_prompt = """Generate exactly 10 questions from this BRD content about container depot operations.

Return ONLY this JSON format with no extra text:
[
{{"question": "question 1", "domain": "domain 1"}},
{{"question": "question 2", "domain": "domain 2"}},
{{"question": "question 3", "domain": "domain 3"}},
{{"question": "question 4", "domain": "domain 4"}},
{{"question": "question 5", "domain": "domain 5"}},
{{"question": "question 6", "domain": "domain 6"}},
{{"question": "question 7", "domain": "domain 7"}},
{{"question": "question 8", "domain": "domain 8"}},
{{"question": "question 9", "domain": "domain 9"}},
{{"question": "question 10", "domain": "domain 10"}}
]

Focus on: reefer operations, yard management, container tracking, integrations, security, reporting, bonded operations.

BRD Content: {document_text}

JSON Response:"""
        
        try:

            limited_text = document_text[:20000] if len(document_text) > 20000 else document_text
            logger.info(f"Using {len(limited_text)} characters of {len(document_text)} total for question generation")
            
            response = self.model.invoke(question_prompt.format(document_text=limited_text))
            
            response_content = response.content if hasattr(response, 'content') else str(response)
            logger.info(f"Raw LLM response: {response_content[:200]}...")

            response_content = response_content.strip()

            if response_content.startswith('```json'):
                response_content = response_content[7:]
            elif response_content.startswith('```'):
                response_content = response_content[3:]

            if response_content.endswith('```'):
                response_content = response_content[:-3]

            response_content = response_content.strip()

            import re
            json_match = re.search(r'\[.*\]', response_content, re.DOTALL)
            if json_match:
                response_content = json_match.group(0)

            logger.info(f"Cleaned response for parsing: {response_content[:200]}...")

            questions_data = json.loads(response_content)

            if not isinstance(questions_data, list):
                logger.error(f"Expected list, got {type(questions_data)}")
                return self._get_fallback_questions()
            
            questions = []
            for q_data in questions_data:
                if isinstance(q_data, dict) and 'question' in q_data:
                    question_text = q_data.get("question", "").strip()
                    if question_text:  
                        questions.append(GeneratedQuestion(
                            question=question_text,
                            domain=q_data.get("domain", "General")
                        ))

            if len(questions) == 0:
                logger.warning("No valid questions parsed from response, using fallback")
                return self._get_fallback_questions()

            logger.info(f"Successfully generated {len(questions)} questions from document")
            return questions
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON parsing failed: {e}")
            logger.error(f"Response content: {response_content if 'response_content' in locals() else 'No content available'}")

            logger.info("Retrying with simpler prompt...")
            return self._retry_with_simple_prompt(limited_text)

        except Exception as e:
            logger.error(f"Error generating questions: {e}")
            logger.info("Retrying with simpler prompt...")
            return self._retry_with_simple_prompt(limited_text)

    def _retry_with_simple_prompt(self, document_text: str) -> List[GeneratedQuestion]:
        """Retry with a very simple prompt that's more likely to work"""
        try:
            simple_prompt = """Based on this document, create 10 questions about container operations.

Return only JSON like this:
[{"question": "What are the reefer requirements?", "domain": "Reefer"}]

Document: {document_text}

JSON:"""

            response = self.model.invoke(simple_prompt.format(document_text=document_text[:5000]))
            response_content = response.content if hasattr(response, 'content') else str(response)

            import re
            json_match = re.search(r'\[.*?\]', response_content, re.DOTALL)
            if json_match:
                try:
                    questions_data = json.loads(json_match.group(0))
                    if isinstance(questions_data, list) and len(questions_data) > 0:
                        questions = []
                        for q_data in questions_data:
                            if isinstance(q_data, dict) and 'question' in q_data:
                                questions.append(GeneratedQuestion(
                                    question=q_data.get("question", ""),
                                    domain=q_data.get("domain", "General")
                                ))
                        if len(questions) > 0:
                            logger.info(f"Retry successful: generated {len(questions)} questions")
                            return questions
                except:
                    pass

            logger.warning("Retry also failed, using fallback questions")
            return self._get_fallback_questions()

        except Exception as e:
            logger.error(f"Retry failed: {e}")
            return self._get_fallback_questions()

    def _get_fallback_questions(self) -> List[GeneratedQuestion]:
        """
        Return fallback questions if generation fails - focused on container depot operations

        Returns:
            List of default GeneratedQuestion objects relevant to container depot management
        """
        return [
            GeneratedQuestion(question="What are the specific reefer operations requirements and temperature management capabilities needed?", domain="Reefer Operations"),
            GeneratedQuestion(question="How should yard management and container positioning be handled in the depot?", domain="Yard Management"),
            GeneratedQuestion(question="What bonded operations and customs compliance workflows are required?", domain="Bonded Operations"),
            GeneratedQuestion(question="How should container tracking and monitoring be implemented across the depot?", domain="Container Tracking"),
            GeneratedQuestion(question="What integration requirements exist with external systems (shipping lines, customs, trucking)?", domain="Integration"),
            GeneratedQuestion(question="What are the user interface requirements for depot workers and managers?", domain="UI/UX"),
            GeneratedQuestion(question="How should stakeholder communication and coordination be managed?", domain="Stakeholder Management"),
            GeneratedQuestion(question="What reporting and analytics capabilities are needed for operational metrics?", domain="Reporting"),
            GeneratedQuestion(question="What are the security and access control requirements for depot operations?", domain="Security"),
            GeneratedQuestion(question="How should temperature excursions and claims management be handled for reefer containers?", domain="Reefer Operations"),
            GeneratedQuestion(question="What performance and scalability requirements exist for high-volume container operations?", domain="Performance"),
            GeneratedQuestion(question="How should the system handle container handshake processes between different stakeholders?", domain="Operational Processes")
        ]
