"""
Question Generation Module
Generates questions from BRD content using LLM
"""

import json
import logging
from typing import List
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

class GeneratedQuestion(BaseModel):
    question: str
    domain: str

class QuestionGenerator:
    """Generates questions from document content using LLM"""
    
    def __init__(self, llm_model):
        """
        Initialize question generator
        
        Args:
            llm_model: LangChain LLM model instance
        """
        if not llm_model:
            raise ValueError("LLM model not available")
        self.model = llm_model
    
    def generate_questions(self, document_text: str) -> List[GeneratedQuestion]:
        """
        Generate relevant questions from document text
        
        Args:
            document_text: The BRD text content
            
        Returns:
            List of GeneratedQuestion objects
        """
        
        question_prompt = """
You are an expert Business Analyst. Analyze the following Business Requirements Document (BRD) and generate 10-15 specific, detailed questions that would help understand the requirements better.

Focus on generating questions about:
- Functional requirements and features
- User interface and user experience  
- System integrations and technical requirements
- Business processes and workflows
- Data management and reporting
- Security and compliance requirements
- Performance and scalability needs
- Container management and yard operations
- Tracking and monitoring capabilities

For each question, also suggest a domain/category (e.g., "UI/UX", "Integration", "Security", "Reporting", "Container Management", etc.)

Return the response as a JSON array with this exact format:
[
  {
    "question": "What are the specific user authentication requirements?",
    "domain": "Security"
  },
  {
    "question": "How should container tracking be implemented in the yard management system?", 
    "domain": "Container Management"
  }
]

IMPORTANT: Return ONLY the JSON array, no other text or formatting.

Document Content:
{document_text}
"""
        
        try:
            # Limit document text to avoid token limits
            limited_text = document_text[:8000] if len(document_text) > 8000 else document_text
            
            response = self.model.invoke(question_prompt.format(document_text=limited_text))
            
            # Extract content from response
            response_content = response.content if hasattr(response, 'content') else str(response)
            
            # Clean up response - remove any markdown formatting
            response_content = response_content.strip()
            if response_content.startswith('```json'):
                response_content = response_content[7:]
            if response_content.endswith('```'):
                response_content = response_content[:-3]
            response_content = response_content.strip()
            
            # Parse JSON response
            questions_data = json.loads(response_content)
            
            questions = []
            for q_data in questions_data:
                if isinstance(q_data, dict) and 'question' in q_data:
                    questions.append(GeneratedQuestion(
                        question=q_data.get("question", ""),
                        domain=q_data.get("domain", "General")
                    ))
            
            logger.info(f"Generated {len(questions)} questions from document")
            return questions
            
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing JSON response: {e}")
            logger.error(f"Response content: {response_content}")
            # Return fallback questions
            return self._get_fallback_questions()
            
        except Exception as e:
            logger.error(f"Error generating questions: {e}")
            # Return fallback questions
            return self._get_fallback_questions()
    
    def _get_fallback_questions(self) -> List[GeneratedQuestion]:
        """
        Return fallback questions if generation fails
        
        Returns:
            List of default GeneratedQuestion objects
        """
        return [
            GeneratedQuestion(question="What are the main functional requirements?", domain="Functional"),
            GeneratedQuestion(question="What are the user interface requirements?", domain="UI/UX"),
            GeneratedQuestion(question="What integration requirements are needed?", domain="Integration"),
            GeneratedQuestion(question="What are the security requirements?", domain="Security"),
            GeneratedQuestion(question="What reporting capabilities are needed?", domain="Reporting"),
            GeneratedQuestion(question="What are the performance requirements?", domain="Performance"),
            GeneratedQuestion(question="How should container tracking be implemented?", domain="Container Management"),
            GeneratedQuestion(question="What data management requirements exist?", domain="Data Management"),
            GeneratedQuestion(question="What are the user access control requirements?", domain="Security"),
            GeneratedQuestion(question="What mobile capabilities are required?", domain="Mobile")
        ]
