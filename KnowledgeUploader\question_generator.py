"""
Question Generation Module
Generates questions from BRD content using LLM
"""

import json
import logging
from typing import List
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

class GeneratedQuestion(BaseModel):
    question: str
    domain: str

class QuestionGenerator:
    """Generates questions from document content using LLM"""
    
    def __init__(self, llm_model):
        """
        Initialize question generator
        
        Args:
            llm_model: LangChain LLM model instance
        """
        if not llm_model:
            raise ValueError("LLM model not available")
        self.model = llm_model
    
    def generate_questions(self, document_text: str) -> List[GeneratedQuestion]:
        """
        Generate relevant questions from document text
        
        Args:
            document_text: The BRD text content
            
        Returns:
            List of GeneratedQuestion objects
        """
        
        question_prompt = """
You are an expert Business Analyst specializing in container depot management and logistics systems. You have deep knowledge of container operations, yard management, and maritime logistics terminology.

CONTEXT: This BRD relates to container depot operations and may include specialized terms such as:
- Reefer operations: Management of refrigerated containers with temperature control
- Yard management: Container positioning, tracking, and movement within depot facilities
- Bonded operations: Customs-controlled container handling and storage
- Container handshake: Data exchange and coordination between systems/stakeholders
- Landside operations: Ground-based container handling (trucks, rail, yard equipment)
- Vessel operations: Ship-based container loading/unloading
- Terminal operations: Port and depot container management
- Setpoint management: Temperature control settings for reefer containers
- Claims management: Handling of damage, loss, or temperature excursion claims
- Transshipment: Container transfer between different transport modes
- OHP (Operational Handover Process): Systematic transfer of operational responsibility

Analyze the following Business Requirements Document (BRD) and generate 10-15 specific, detailed questions that would help understand the requirements better, keeping in mind the container depot and logistics context.

Focus on generating questions about:
- Functional requirements and features specific to container operations
- User interface and user experience for depot workers, managers, and stakeholders
- System integrations between depot systems, shipping lines, customs, and logistics partners
- Business processes and workflows for container handling, tracking, and documentation
- Data management and reporting for operational metrics, compliance, and performance
- Security and compliance requirements for customs, cargo, and operational safety
- Performance and scalability needs for high-volume container operations
- Container management including reefer operations, bonded storage, and yard positioning
- Tracking and monitoring capabilities for containers, equipment, and operational status
- Temperature management and monitoring for reefer containers
- Stakeholder coordination and communication workflows
- Integration with external systems (customs, shipping lines, trucking companies)

For each question, suggest a relevant domain/category such as: "Reefer Operations", "Yard Management", "Integration", "Security", "Reporting", "Container Tracking", "Temperature Management", "Bonded Operations", "UI/UX", "Performance", "Compliance", "Stakeholder Management", etc.

Return the response as a JSON array with this exact format:
[
  {
    "question": "What are the specific reefer setpoint management requirements and how should temperature changes be communicated to stakeholders?",
    "domain": "Reefer Operations"
  },
  {
    "question": "How should the system handle bonded container operations and customs compliance workflows?",
    "domain": "Bonded Operations"
  }
]

IMPORTANT: Return ONLY the JSON array, no other text or formatting.

Document Content:
{document_text}
"""
        
        try:

            limited_text = document_text[:20000] if len(document_text) > 20000 else document_text
            logger.info(f"Using {len(limited_text)} characters of {len(document_text)} total for question generation")
            
            response = self.model.invoke(question_prompt.format(document_text=limited_text))
            
            response_content = response.content if hasattr(response, 'content') else str(response)
            logger.info(f"Raw LLM response: {response_content[:200]}...")

            response_content = response_content.strip()

            if response_content.startswith('```json'):
                response_content = response_content[7:]
            elif response_content.startswith('```'):
                response_content = response_content[3:]

            if response_content.endswith('```'):
                response_content = response_content[:-3]

            response_content = response_content.strip()

            import re
            json_match = re.search(r'\[.*\]', response_content, re.DOTALL)
            if json_match:
                response_content = json_match.group(0)

            logger.info(f"Cleaned response for parsing: {response_content[:200]}...")

            questions_data = json.loads(response_content)

            if not isinstance(questions_data, list):
                logger.error(f"Expected list, got {type(questions_data)}")
                return self._get_fallback_questions()
            
            questions = []
            for q_data in questions_data:
                if isinstance(q_data, dict) and 'question' in q_data:
                    question_text = q_data.get("question", "").strip()
                    if question_text:  # Only add non-empty questions
                        questions.append(GeneratedQuestion(
                            question=question_text,
                            domain=q_data.get("domain", "General")
                        ))

            if len(questions) == 0:
                logger.warning("No valid questions parsed from response, using fallback")
                return self._get_fallback_questions()

            logger.info(f"Successfully generated {len(questions)} questions from document")
            return questions
            
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing JSON response: {e}")
            logger.error(f"Response content: {response_content if 'response_content' in locals() else 'No content available'}")
            return self._get_fallback_questions()

        except Exception as e:
            logger.error(f"Error generating questions: {e}")
            return self._get_fallback_questions()

    def _get_fallback_questions(self) -> List[GeneratedQuestion]:
        """
        Return fallback questions if generation fails - focused on container depot operations

        Returns:
            List of default GeneratedQuestion objects relevant to container depot management
        """
        return [
            GeneratedQuestion(question="What are the specific reefer operations requirements and temperature management capabilities needed?", domain="Reefer Operations"),
            GeneratedQuestion(question="How should yard management and container positioning be handled in the depot?", domain="Yard Management"),
            GeneratedQuestion(question="What bonded operations and customs compliance workflows are required?", domain="Bonded Operations"),
            GeneratedQuestion(question="How should container tracking and monitoring be implemented across the depot?", domain="Container Tracking"),
            GeneratedQuestion(question="What integration requirements exist with external systems (shipping lines, customs, trucking)?", domain="Integration"),
            GeneratedQuestion(question="What are the user interface requirements for depot workers and managers?", domain="UI/UX"),
            GeneratedQuestion(question="How should stakeholder communication and coordination be managed?", domain="Stakeholder Management"),
            GeneratedQuestion(question="What reporting and analytics capabilities are needed for operational metrics?", domain="Reporting"),
            GeneratedQuestion(question="What are the security and access control requirements for depot operations?", domain="Security"),
            GeneratedQuestion(question="How should temperature excursions and claims management be handled for reefer containers?", domain="Reefer Operations"),
            GeneratedQuestion(question="What performance and scalability requirements exist for high-volume container operations?", domain="Performance"),
            GeneratedQuestion(question="How should the system handle container handshake processes between different stakeholders?", domain="Operational Processes")
        ]
