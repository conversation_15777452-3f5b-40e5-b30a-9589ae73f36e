"""
model.py - Language model interface for TORI conversational AI system

This module provides a factory class for accessing various Azure OpenAI language models
used throughout the application. It encapsulates model initialization logic, implements
caching for model instances, and provides a consistent interface for obtaining different
model types (chat models and embeddings).

The module relies on configuration parameters imported from the config module and
supports multiple Azure OpenAI models including o1, o1-mini, gpt-4o, and gpt-4o-mini.
"""

from functools import lru_cache

from langchain_openai import AzureChatOpenAI, AzureOpenAIEmbeddings
from langchain_community.chat_message_histories import ChatMessageHistory

from config import (
    AZURE_OPENAI_API_KEY_4_1,
    AZURE_OPENAI_ENDPOINT_4_1,
    AZURE_OPENAI_VERSION_4_1,
    EMBEDDING_MODEL,
    EMBEDDING_OPENAI_API_BASE,
    EMBEDDING_OPENAI_API_KEY,
    EMBEDDING_OPENAI_API_VERSION,
    AZURE_OPENAI_API_KEY_4o,
    AZURE_OPENAI_API_KEY_o1,
    AZURE_OPENAI_API_KEY_o1mini,
    AZURE_OPENAI_ENDPOINT_4o,
    AZURE_OPENAI_ENDPOINT_o1,
    AZURE_OPENAI_ENDPOINT_o1mini,
    AZURE_OPENAI_VERSION_4o,
    AZURE_OPENAI_VERSION_o1,
    AZURE_OPENAI_VERSION_o1mini,
    DEFAULT_AI_MODEL
)


chat_history = ChatMessageHistory()


class Model:
    """
    Factory class for accessing various language models used by the TORI system.

    This class provides static methods to initialize different Azure OpenAI models
    with appropriate configuration parameters. It serves as a centralized access
    point for all language model interactions in the application and implements
    caching to avoid recreating model instances unnecessarily.

    Attributes:
        _model_cache (dict): Internal cache of model instances indexed by model name
    """

    # Cache of model instances to avoid recreating them
    _model_cache = {}

    @staticmethod
    @lru_cache(maxsize=8)
    def get_model(model: str="default", verbose=False):
        """
        Factory method to get the appropriate language model instance based on model name.
        Uses caching to avoid recreating model instances for better performance.

        Args:
            model (str, optional): The model identifier. Supported values are:
                                   "o1", "o1-mini", "gpt-4o", "gpt-4o-mini", "embedding".
                                   Defaults to "o1".

        Returns:
            LangChain model instance: Initialized model for use in the application

        Raises:
            ValueError: When an unsupported model name is provided
        """
        # Check if model is already in cache
        if model in Model._model_cache:
            return Model._model_cache[model]

        if model == "default":
            model = DEFAULT_AI_MODEL

        _model_map = {
            "o1": Model.get_o1_model,
            "o1-mini": Model.get_o1_mini_model,
            "gpt-4o": Model.get_4o_model,
            "gpt-4o-mini": Model.get_4o_mini_model,
            "gpt-4.1": Model.get_4_1_model,
            "embedding": Model.get_embedding_model
        }

        if model not in _model_map:
            raise ValueError(f"Unsupported model: {model}. Supported models are: {list(_model_map.keys())}")

        return _model_map.get(model)(verbose=verbose)
    @staticmethod
    def get_o1_model(verbose=False):
        """
        Initialize and return an Azure OpenAI 'o1' chat model.

        Returns:
            AzureChatOpenAI: Initialized o1 model instance
        """
        return AzureChatOpenAI(
            openai_api_key=AZURE_OPENAI_API_KEY_o1,
            azure_endpoint=AZURE_OPENAI_ENDPOINT_o1,
            openai_api_version=AZURE_OPENAI_VERSION_o1,
            deployment_name="o1",
            temperature=0.0,
            verbose=verbose
        )

    @staticmethod
    def get_o1_mini_model(verbose=False):
        """
        Initialize and return an Azure OpenAI 'o1-mini' chat model.

        Returns:
            AzureChatOpenAI: Initialized o1-mini model instance
        """
        return AzureChatOpenAI(
            openai_api_key=AZURE_OPENAI_API_KEY_o1mini,
            azure_endpoint=AZURE_OPENAI_ENDPOINT_o1mini,
            openai_api_version=AZURE_OPENAI_VERSION_o1mini,
            deployment_name="o1-mini",
            temperature=0.0,
            verbose=verbose
        )

    @staticmethod
    def get_4o_model(verbose=False):
        """
        Initialize and return an Azure OpenAI 'gpt-4o' chat model.

        Returns:
            AzureChatOpenAI: Initialized gpt-4o model instance
        """
        return AzureChatOpenAI(
            openai_api_key=AZURE_OPENAI_API_KEY_4o,
            azure_endpoint=AZURE_OPENAI_ENDPOINT_4o,
            openai_api_version=AZURE_OPENAI_VERSION_4o,
            deployment_name="gpt-4o",
            temperature=0.0,
            verbose=verbose
        )

    @staticmethod
    def get_4o_mini_model(verbose=False):
        """
        Initialize and return an Azure OpenAI 'gpt-4o-mini' chat model.

        Returns:
            AzureChatOpenAI: Initialized gpt-4o-mini model instance
        """
        return AzureChatOpenAI(
            openai_api_key=AZURE_OPENAI_API_KEY_4o,
            azure_endpoint=AZURE_OPENAI_ENDPOINT_4o,
            openai_api_version=AZURE_OPENAI_VERSION_4o,
            deployment_name="gpt-4o-mini",
            temperature=0.0,
            verbose=verbose
        )

    @staticmethod
    def get_4_1_model(verbose=False):
        """
        Initialize and return an Azure OpenAI 'gpt-41' chat model.

        Returns:
            AzureChatOpenAI: Initialized gpt-41 model instance
        """

        return AzureChatOpenAI(
            openai_api_key=AZURE_OPENAI_API_KEY_4_1,
            azure_endpoint=AZURE_OPENAI_ENDPOINT_4_1,
            openai_api_version=AZURE_OPENAI_VERSION_4_1,
            deployment_name="gpt-4.1",
            temperature=0.0,
            verbose=verbose
        )

    @staticmethod
    def get_embedding_model(verbose=False):
        """
        Initialize and return an Azure OpenAI embedding model.

        Returns:
            AzureOpenAIEmbeddings: Initialized embedding model instance
        """
        # Create embedding model
        # verbose not supported for this model
        embedding_llm = AzureOpenAIEmbeddings(
            deployment=EMBEDDING_MODEL,
            model=EMBEDDING_MODEL,
            chunk_size=1,
            openai_api_type="azure",
            azure_endpoint=EMBEDDING_OPENAI_API_BASE,
            openai_api_version=EMBEDDING_OPENAI_API_VERSION,
            openai_api_key=EMBEDDING_OPENAI_API_KEY
        )
        return embedding_llm
