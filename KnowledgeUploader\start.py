#!/usr/bin/env python3
"""
Cross-platform startup script for Enhanced BA Agent API
Works on Windows, Linux, and Mac
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def run_command(cmd, shell=False):
    """Run a command and return success status"""
    try:
        result = subprocess.run(cmd, shell=shell, check=True, capture_output=True, text=True)
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, e.stderr

def main():
    print("🚀 Enhanced BA Agent API Startup Script")
    print("=" * 50)
    
    # Get current directory
    current_dir = Path(__file__).parent
    os.chdir(current_dir)
    
    # Check Python version
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print("❌ Error: Python 3.8+ is required")
        print(f"   Current version: {python_version.major}.{python_version.minor}")
        sys.exit(1)
    
    print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro} detected")
    
    # Check if virtual environment exists
    venv_path = current_dir / ".venv"
    if not venv_path.exists():
        print("📦 Creating virtual environment...")
        success, output = run_command([sys.executable, "-m", "venv", ".venv"])
        if not success:
            print(f"❌ Failed to create virtual environment: {output}")
            sys.exit(1)
        print("✅ Virtual environment created")
    else:
        print("✅ Virtual environment found")
    
    # Determine activation script path
    system = platform.system().lower()
    if system == "windows":
        activate_script = venv_path / "Scripts" / "activate.bat"
        pip_path = venv_path / "Scripts" / "pip.exe"
        python_path = venv_path / "Scripts" / "python.exe"
    else:
        activate_script = venv_path / "bin" / "activate"
        pip_path = venv_path / "bin" / "pip"
        python_path = venv_path / "bin" / "python"
    
    # Install/upgrade pip
    print("📦 Upgrading pip...")
    success, output = run_command([str(python_path), "-m", "pip", "install", "--upgrade", "pip"])
    if success:
        print("✅ Pip upgraded")
    else:
        print(f"⚠️  Pip upgrade warning: {output}")
    
    # Install dependencies
    requirements_file = current_dir / "requirements.txt"
    if requirements_file.exists():
        print("📦 Installing dependencies...")
        success, output = run_command([str(pip_path), "install", "-r", "requirements.txt"])
        if not success:
            print(f"❌ Failed to install dependencies: {output}")
            sys.exit(1)
        print("✅ Dependencies installed")
    else:
        print("⚠️  requirements.txt not found, skipping dependency installation")
    
    # Check for .env file
    env_file = current_dir.parent / ".env"
    if not env_file.exists():
        print("❌ Error: .env file not found!")
        print(f"   Expected location: {env_file}")
        print("   Please ensure the .env file exists with the required configuration.")
        sys.exit(1)
    print("✅ .env file found")
    
    # Start the server
    print("\n" + "=" * 50)
    print("🚀 Starting Enhanced BA Agent API server...")
    print("📡 API will be available at: http://localhost:8000")
    print("🔗 Main endpoint: POST http://localhost:8000/process-brd")
    print("💚 Health check: GET http://localhost:8000/health")
    print("📚 API documentation: http://localhost:8000/docs")
    print("=" * 50)
    print()
    
    # Run the BA Agent
    try:
        subprocess.run([str(python_path), "BA_Agent.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start BA Agent: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        sys.exit(0)

if __name__ == "__main__":
    main()
