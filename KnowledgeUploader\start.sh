#!/bin/bash

# Enhanced BA Agent API Startup Script

echo "Starting Enhanced BA Agent API..."

# Detect Python command
PYTHON_CMD="python3"
if ! command -v python3 &> /dev/null; then
    if command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        echo "Error: Python not found. Please install Python."
        exit 1
    fi
fi

echo "Using Python command: $PYTHON_CMD"

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    echo "Creating virtual environment..."
    $PYTHON_CMD -m venv .venv
fi

# Activate virtual environment
echo "Activating virtual environment..."
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
    # Windows Git Bash
    source .venv/Scripts/activate
else
    # Linux/Mac
    source .venv/bin/activate
fi

# Upgrade pip
echo "Upgrading pip..."
pip install --upgrade pip

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

# Check if .env file exists
if [ ! -f "../.env" ]; then
    echo "Error: .env file not found in parent directory!"
    echo "Please ensure the .env file exists with the required configuration."
    echo "Expected location: $(pwd)/../.env"
    exit 1
fi

echo ""
echo "Starting Enhanced BA Agent API server..."
echo "API will be available at: http://localhost:8000"
echo "Main endpoint: POST http://localhost:8000/process-brd"
echo "Health check: GET http://localhost:8000/health"
echo "API documentation: http://localhost:8000/docs"
echo ""

# Start the server
python BA_Agent.py
