#!/bin/bash

# Enhanced BA Agent API Startup Script

echo "Starting Enhanced BA Agent API..."

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv .venv
fi

# Activate virtual environment
echo "Activating virtual environment..."
source .venv/bin/activate

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

# Check if .env file exists
if [ ! -f "../.env" ]; then
    echo "Error: .env file not found in parent directory!"
    echo "Please ensure the .env file exists with the required configuration."
    exit 1
fi

# Start the Enhanced BA Agent API server
echo "Starting Enhanced BA Agent API server..."
echo "API will be available at: http://localhost:8000"
echo "Main endpoint: POST http://localhost:8000/process-brd"
echo "Health check: GET http://localhost:8000/health"
echo "API documentation: http://localhost:8000/docs"

# Option 1: Direct Python execution (recommended for your setup)
python BA_Agent.py

# Option 2: Using uvicorn (alternative)
# python -m uvicorn BA_Agent:app --host 0.0.0.0 --port 8000 --reload
