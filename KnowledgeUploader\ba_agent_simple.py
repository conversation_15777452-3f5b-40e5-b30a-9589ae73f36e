#!/usr/bin/env python3
"""
Simplified BA Agent using QdrantHTTPClient
This version works without <PERSON><PERSON><PERSON><PERSON> dependencies for basic testing
"""

import os
import sys
import json
import logging
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.append(os.path.dirname(__file__))

from qdrant_http_client import QdrantHTTPClient

logger = logging.getLogger(__name__)

class SimpleQdrantRetriever:
    """Simple Qdrant retriever using HTTP client"""
    
    def __init__(self, client: QdrantHTTPClient, collection_name: str):
        self.client = client
        self.collection_name = collection_name
    
    def search_relevant_chunks(
        self,
        query_vector: List[float],
        limit: int = 10,
        score_threshold: float = 0.7,
        document_filter: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Search for relevant chunks using vector similarity"""
        try:
            print(f"Searching in collection '{self.collection_name}'")
            
            # Use HTTP client search
            search_results = self.client.search_points(
                collection_name=self.collection_name,
                query_vector=query_vector,
                limit=limit,
                score_threshold=score_threshold
            )
            
            formatted_results = []
            # Handle HTTP response format
            if 'result' in search_results:
                points = search_results['result']
            else:
                points = search_results.get('points', [])
                
            for point in points:
                try:
                    payload = point.get("payload", {})
                    
                    # Filter by document if specified
                    if document_filter and payload.get('document_name') != document_filter:
                        continue
                        
                    if "_node_content" in payload:
                        node = json.loads(payload["_node_content"])
                        content = node.get("text", "")
                        metadata = node.get("metadata", {})
                    else:
                        content = payload.get("content", "")
                        metadata = payload.get("metadata", {})
                        
                except (json.JSONDecodeError, KeyError):
                    content = payload.get("content", "")
                    metadata = payload.get("metadata", {})

                formatted_results.append({
                    'content': content,
                    'metadata': metadata,
                    'score': point.get('score', 0),
                    'document_name': payload.get('document_name', ''),
                    'chunk_id': payload.get('chunk_id', str(point.get('id', '')))
                })

            print(f"Found {len(formatted_results)} relevant chunks")
            return formatted_results

        except Exception as e:
            logger.error(f"Error in search: {e}")
            return []
    
    def get_all_documents(self) -> List[str]:
        """Get list of all available documents in the collection"""
        try:
            # Use scroll to get all points
            scroll_results = self.client.scroll_points(
                collection_name=self.collection_name,
                limit=1000,
                with_payload=True
            )

            documents = set()
            if 'result' in scroll_results:
                points = scroll_results['result'].get('points', [])
                for point in points:
                    payload = point.get('payload', {})
                    doc_name = payload.get('document_name', '')
                    if doc_name:
                        documents.add(doc_name)

            return list(documents)
        except Exception as e:
            logger.error(f"Error getting documents: {e}")
            return []

    def get_comprehensive_context(
        self,
        document_name: Optional[str] = None,
        dummy_vector: Optional[List[float]] = None
    ) -> str:
        """Get comprehensive context using scroll (no vector needed)"""

        print("Retrieving comprehensive context...")

        try:
            # Use scroll to get all points
            scroll_results = self.client.scroll_points(
                collection_name=self.collection_name,
                limit=1000,
                with_payload=True
            )

            chunks = []
            if 'result' in scroll_results:
                points = scroll_results['result'].get('points', [])
                for point in points:
                    payload = point.get('payload', {})

                    # Filter by document if specified
                    if document_name and payload.get('document_name') != document_name:
                        continue

                    try:
                        if "_node_content" in payload:
                            node = json.loads(payload["_node_content"])
                            content = node.get("text", "")
                            metadata = node.get("metadata", {})
                        else:
                            content = payload.get("content", "")
                            metadata = payload.get("metadata", {})
                    except (json.JSONDecodeError, KeyError):
                        content = payload.get("content", "")
                        metadata = payload.get("metadata", {})

                    if content.strip():
                        chunks.append({
                            'content': content,
                            'metadata': metadata,
                            'document_name': payload.get('document_name', ''),
                            'chunk_id': payload.get('chunk_id', str(point.get('id', '')))
                        })

            print(f"Found {len(chunks)} content chunks")
            combined_content = [chunk['content'] for chunk in chunks]
            return "\n\n".join(combined_content)

        except Exception as e:
            logger.error(f"Error getting context: {e}")
            return ""

def initialize_qdrant_http_client() -> QdrantHTTPClient:
    """Initialize Qdrant HTTP client"""
    qdrant_url = os.getenv("QDRANT_ENDPOINT")
    qdrant_api_key = os.getenv("QDRANT_API_KEY")
    
    if not qdrant_url:
        raise ValueError("QDRANT_ENDPOINT environment variable is not set")
    if not qdrant_api_key:
        raise ValueError("QDRANT_API_KEY environment variable is not set")

    print(f"Connecting to Qdrant at: {qdrant_url}")
    return QdrantHTTPClient(
        url=qdrant_url,
        api_key=qdrant_api_key,
        timeout=60
    )

def test_qdrant_connection(client: QdrantHTTPClient, collection_name: str) -> bool:
    """Test Qdrant connection and collection availability"""
    try:
        collections = client.get_collections()
        collection_names = [c['name'] for c in collections.get('result', {}).get('collections', [])]
        print(f"Available collections: {collection_names}")
        
        if collection_name not in collection_names:
            raise ValueError(f"Collection '{collection_name}' not found in Qdrant")
            
        collection_info = client.get_collection_info(collection_name)
        points_count = collection_info.get('result', {}).get('points_count', 0)
        print(f"Collection '{collection_name}' found with {points_count} points")
        return True
        
    except Exception as e:
        print(f"Qdrant connection test failed: {e}")
        return False

def retrieve_context_from_vector_store(document_name: str) -> str:
    """Retrieve context from vector store using HTTP client"""
    print("=" * 60)
    print("BA Agent Vector Store Search (Simple Version)")
    print("=" * 60)
    
    try:
        # Initialize client
        qdrant_client = initialize_qdrant_http_client()
        collection_name = os.getenv('QDRANT_BRD_COLLECTION_NAME')
        
        if not collection_name:
            raise ValueError("QDRANT_BRD_COLLECTION_NAME environment variable is not set")
        
        # Test connection
        if not test_qdrant_connection(qdrant_client, collection_name):
            raise ValueError("Cannot connect to Qdrant database")
        
        # Initialize retriever
        retriever = SimpleQdrantRetriever(
            client=qdrant_client,
            collection_name=collection_name
        )

        # First, get list of available documents
        available_docs = retriever.get_all_documents()
        print(f"Available documents: {available_docs}")

        # Check if requested document exists
        if document_name not in available_docs:
            print(f"⚠️  Document '{document_name}' not found. Available documents: {available_docs}")
            if available_docs:
                document_name = available_docs[0]
                print(f"Using first available document: {document_name}")
            else:
                document_name = None
                print("No document filter - retrieving all content")

        # Get context
        context = retriever.get_comprehensive_context(document_name=document_name)
        
        if not context:
            raise ValueError(f"No relevant context found for document: {document_name}")
        
        print(f"Retrieved context length: {len(context)} characters")
        return context
        
    except Exception as e:
        print(f"Error retrieving context: {e}")
        raise

def main():
    """Main function for testing context retrieval"""
    print("Simple BA Agent - Context Retrieval Test")
    print("=" * 50)
    
    try:
        document_name = "BRD-SD1.docx"
        print(f"Retrieving context for document: {document_name}")
        
        context = retrieve_context_from_vector_store(document_name)
        
        # Save context to file
        output_file = "retrieved_context.txt"
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(f"Document: {document_name}\n")
            f.write("=" * 50 + "\n\n")
            f.write(context)
        
        print(f"\n✅ Context retrieved successfully!")
        print(f"📄 Context saved to: {output_file}")
        print(f"📊 Context length: {len(context)} characters")
        
        # Show preview
        preview = context[:500] + "..." if len(context) > 500 else context
        print(f"\n📖 Context Preview:")
        print("-" * 30)
        print(preview)
        print("-" * 30)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
