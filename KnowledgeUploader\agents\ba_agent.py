"""
Enhanced BA Agent - FastAPI Application
Single endpoint BRD processing: Upload DOCX → Generate Questions → Search FAQ → Create User Stories
"""

import os
import logging
import tempfile
from typing import List
from fastapi import FastAPI, HTTPException, UploadFile, File
from pydantic import BaseModel
from dotenv import load_dotenv

from utils.brd_processor import BRDProcessor
from utils.question_generator import QuestionGenerator
from utils.faq_searcher import FAQSearcher
from utils.story_generator import UserStoryGenerator, Jira<PERSON>pic
from qdrant_http_client import QdrantHTTPClient

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# FastAPI app
app = FastAPI(
    title="Jarvis Knowledge - Enhanced BA Agent API", 
    description="AI-powered BRD processing: Upload DOCX → Generate Questions → Search FAQ → Create User Stories",
    version="1.0.0"
)

# Response model with epic wrapper
class EpicResponse(BaseModel):
    epic: <PERSON>raEpic

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Jarvis Knowledge - Enhanced BA Agent API",
        "version": "1.0.0",
        "status": "operational"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint with system status"""
    try:
        # Check environment variables
        required_vars = [
            "AZURE_OPENAI_ENDPOINT_4_1",
            "AZURE_OPENAI_API_KEY_4_1",
            "QDRANT_ENDPOINT",
            "QDRANT_API_KEY"
        ]
        
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        
        if missing_vars:
            return {
                "status": "unhealthy",
                "error": f"Missing environment variables: {', '.join(missing_vars)}"
            }
        
        # Test Qdrant connection
        qdrant_endpoint = os.getenv("QDRANT_ENDPOINT")
        qdrant_api_key = os.getenv("QDRANT_API_KEY")
        
        if qdrant_endpoint and qdrant_api_key:
            qdrant_client = QdrantHTTPClient(qdrant_endpoint, qdrant_api_key)
            collections = qdrant_client.get_collections()
            
            return {
                "status": "healthy",
                "qdrant_connected": True,
                "collections_available": len(collections.get("result", {}).get("collections", [])),
                "environment": "configured"
            }
        else:
            return {
                "status": "unhealthy",
                "error": "Qdrant configuration missing"
            }
            
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }

@app.get("/collections")
async def list_collections():
    """List available Qdrant collections"""
    try:
        qdrant_endpoint = os.getenv("QDRANT_ENDPOINT")
        qdrant_api_key = os.getenv("QDRANT_API_KEY")
        
        if not qdrant_endpoint or not qdrant_api_key:
            raise HTTPException(status_code=500, detail="Qdrant configuration missing")
        
        qdrant_client = QdrantHTTPClient(qdrant_endpoint, qdrant_api_key)
        collections_response = qdrant_client.get_collections()
        collections = collections_response.get("result", {}).get("collections", [])
        
        return {
            "collections": [
                {
                    "name": collection.get("name"),
                    "status": "available"
                }
                for collection in collections
            ]
        }
        
    except Exception as e:
        logger.error(f"Error listing collections: {e}")
        raise HTTPException(status_code=500, detail=f"Error listing collections: {str(e)}")

@app.post("/process-brd", response_model=EpicResponse)
async def process_brd_complete(file: UploadFile = File(...)):
    """
    Complete BRD processing pipeline:
    1. Extract text from DOCX
    2. Generate questions using LLM
    3. Search FAQ database for answers
    4. Generate user stories with combined context
    """
    
    temp_file_path = None
    
    try:
        # Validate file
        if not file.filename.endswith('.docx'):
            raise HTTPException(status_code=400, detail="Only DOCX files are supported")
        
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        logger.info(f"Processing BRD file: {file.filename}")
        
        # Step 1: Extract text from DOCX
        document_text = BRDProcessor.extract_text_from_docx(temp_file_path)
        logger.info(f"Extracted {len(document_text)} characters from document")
        
        # Step 2: Generate questions
        from langchain_openai import AzureChatOpenAI
        
        model = AzureChatOpenAI(
            azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT_4_1"),
            api_key=os.getenv("AZURE_OPENAI_API_KEY_4_1"),
            api_version=os.getenv("AZURE_OPENAI_VERSION_4_1", "2025-01-01-preview"),
            deployment_name="gpt-4",
            temperature=0.1
        )
        
        question_generator = QuestionGenerator(model)
        questions = question_generator.generate_questions(document_text)
        logger.info(f"Generated {len(questions)} questions")
        
        # Step 3: Search FAQ database
        qdrant_endpoint = os.getenv("QDRANT_ENDPOINT")
        qdrant_api_key = os.getenv("QDRANT_API_KEY")
        faq_collection = os.getenv("QDRANT_BRD_COLLECTION_NAME")
        
        if not all([qdrant_endpoint, qdrant_api_key, faq_collection]):
            raise HTTPException(status_code=500, detail="Qdrant configuration incomplete")
        
        qdrant_client = QdrantHTTPClient(qdrant_endpoint, qdrant_api_key)
        
        # Initialize embedding model
        from langchain_openai import AzureOpenAIEmbeddings
        
        embedding_llm = AzureOpenAIEmbeddings(
            azure_endpoint=os.getenv("EMBEDDING_OPENAI_API_BASE"),
            api_key=os.getenv("EMBEDDING_OPENAI_API_KEY"),
            api_version=os.getenv("EMBEDDING_OPENAI_API_VERSION", "2023-05-15"),
            model="text-embedding-3-large"
        )
        
        if not qdrant_client.test_connection():
            raise HTTPException(status_code=500, detail="Cannot connect to Qdrant database")
        
        if not embedding_llm:
            raise HTTPException(status_code=500, detail="Embedding model not available")
        
        faq_searcher = FAQSearcher(qdrant_client, faq_collection, embedding_llm)
        faq_answers = faq_searcher.search_faq_answers(questions)
        
        # Step 4: Generate user stories
        story_generator = UserStoryGenerator(model)
        epic = story_generator.generate_user_stories(document_text, faq_answers)
        
        # Return epic wrapped in response format
        return EpicResponse(epic=epic)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing BRD: {e}")
        raise HTTPException(status_code=500, detail=f"Error processing BRD: {str(e)}")
    finally:
        BRDProcessor.cleanup_temp_file(temp_file_path)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
