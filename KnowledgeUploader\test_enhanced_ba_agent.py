#!/usr/bin/env python3
"""
Test script for the Enhanced BA Agent API
Tests the complete workflow: Upload -> Questions -> FAQ Search -> User Stories
"""

import requests
import json
import os
from pathlib import Path

# API base URL
BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test the health endpoint"""
    print("🔍 Testing Health Check...")
    response = requests.get(f"{BASE_URL}/health")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Health Check: {data}")
        return True
    else:
        print(f"❌ Health Check Failed: {response.status_code}")
        return False

def test_upload_document(file_path):
    """Test document upload"""
    print(f"📤 Testing Document Upload: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return None
    
    with open(file_path, 'rb') as f:
        files = {'file': (os.path.basename(file_path), f, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')}
        response = requests.post(f"{BASE_URL}/upload-document", files=files)
    
    if response.status_code == 200:
        data = response.json()
        if data['success']:
            print(f"✅ Upload Success: Document ID = {data['document_id']}")
            print(f"   Filename: {data['filename']}")
            print(f"   Text Length: {data['text_length']} characters")
            return data['document_id']
        else:
            print(f"❌ Upload Failed: {data.get('error')}")
            return None
    else:
        print(f"❌ Upload Request Failed: {response.status_code}")
        return None

def test_generate_questions(document_id):
    """Test question generation"""
    print(f"❓ Testing Question Generation for Document: {document_id}")
    
    response = requests.post(f"{BASE_URL}/generate-questions/{document_id}")
    
    if response.status_code == 200:
        data = response.json()
        if data['success']:
            print(f"✅ Questions Generated: {data['questions_count']} questions")
            for i, question in enumerate(data['questions'][:3], 1):  # Show first 3
                print(f"   {i}. [{question.get('domain', 'General')}] {question['question']}")
            if len(data['questions']) > 3:
                print(f"   ... and {len(data['questions']) - 3} more questions")
            return True
        else:
            print(f"❌ Question Generation Failed: {data.get('error')}")
            return False
    else:
        print(f"❌ Question Generation Request Failed: {response.status_code}")
        return False

def test_search_faq(document_id, collection_name=None):
    """Test FAQ search"""
    print(f"🔍 Testing FAQ Search for Document: {document_id}")
    
    payload = {"document_id": document_id}
    if collection_name:
        payload["collection_name"] = collection_name
    
    response = requests.post(f"{BASE_URL}/search-faq/{document_id}", json=payload)
    
    if response.status_code == 200:
        data = response.json()
        if data['success']:
            print(f"✅ FAQ Search Complete: {data['answers_found']} answers found")
            for i, faq in enumerate(data['faq_answers'][:2], 1):  # Show first 2
                print(f"   {i}. Q: {faq['question'][:80]}...")
                print(f"      A: {faq['answer'][:100]}...")
                print(f"      Score: {faq['score']:.2f}, Source: {faq.get('source_document', 'Unknown')}")
            if len(data['faq_answers']) > 2:
                print(f"   ... and {len(data['faq_answers']) - 2} more FAQ answers")
            return True
        else:
            print(f"❌ FAQ Search Failed: {data.get('error')}")
            return False
    else:
        print(f"❌ FAQ Search Request Failed: {response.status_code}")
        return False

def test_generate_user_stories(document_id):
    """Test user story generation"""
    print(f"📝 Testing User Story Generation for Document: {document_id}")
    
    response = requests.post(f"{BASE_URL}/generate-user-stories/{document_id}")
    
    if response.status_code == 200:
        data = response.json()
        if data['success']:
            epic = data['epic']
            print(f"✅ User Stories Generated Successfully!")
            print(f"   Epic: {epic['title']}")
            print(f"   Description: {epic['description'][:100]}...")
            print(f"   Stories Count: {len(epic['issues'])}")
            print(f"   Questions Used: {data.get('questions_count', 0)}")
            print(f"   FAQ Answers Used: {data.get('faq_answers_count', 0)}")
            
            # Show first story
            if epic['issues']:
                story = epic['issues'][0]
                print(f"\n   📋 Sample Story:")
                print(f"      Title: {story['title']}")
                print(f"      Description: {story['description'][:100]}...")
                print(f"      Priority: {story['priority']}, Points: {story['story_points']}")
            
            return True
        else:
            print(f"❌ User Story Generation Failed: {data.get('error')}")
            return False
    else:
        print(f"❌ User Story Generation Request Failed: {response.status_code}")
        return False

def test_complete_workflow(file_path):
    """Test the complete workflow endpoint"""
    print(f"🚀 Testing Complete Workflow: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    with open(file_path, 'rb') as f:
        files = {'file': (os.path.basename(file_path), f, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')}
        response = requests.post(f"{BASE_URL}/process-brd-complete", files=files)
    
    if response.status_code == 200:
        data = response.json()
        if data['success']:
            print(f"✅ Complete Workflow Success!")
            print(f"   Document ID: {data['document_id']}")
            print(f"   Epic: {data['epic']['title']}")
            print(f"   Stories: {len(data['epic']['issues'])}")
            return True
        else:
            print(f"❌ Complete Workflow Failed: {data.get('error')}")
            return False
    else:
        print(f"❌ Complete Workflow Request Failed: {response.status_code}")
        return False

def main():
    print("🧪 Enhanced BA Agent API Test Suite")
    print("=" * 60)
    
    # Test health check
    if not test_health_check():
        print("❌ Health check failed. Make sure the server is running.")
        return
    
    print("\n" + "=" * 60)
    
    # Look for a sample DOCX file
    sample_files = [
        "sample_brd.docx",
        "../sample/materials/BRD-SD1.docx",
        "test_document.docx"
    ]
    
    test_file = None
    for file_path in sample_files:
        if os.path.exists(file_path):
            test_file = file_path
            break
    
    if not test_file:
        print("⚠️  No sample DOCX file found. Please provide a DOCX file for testing.")
        print("   Expected files: sample_brd.docx, test_document.docx")
        print("\n🔧 You can still test individual endpoints with Bruno:")
        print_bruno_examples()
        return
    
    print(f"📄 Using test file: {test_file}")
    
    # Test step-by-step workflow
    print("\n🔄 Testing Step-by-Step Workflow:")
    print("-" * 40)
    
    # Step 1: Upload
    document_id = test_upload_document(test_file)
    if not document_id:
        return
    
    # Step 2: Generate questions
    if not test_generate_questions(document_id):
        return
    
    # Step 3: Search FAQ
    if not test_search_faq(document_id):
        return
    
    # Step 4: Generate user stories
    if not test_generate_user_stories(document_id):
        return
    
    print("\n🚀 Testing Complete Workflow Endpoint:")
    print("-" * 40)
    test_complete_workflow(test_file)
    
    print("\n" + "=" * 60)
    print("✅ All tests completed!")
    print("\n📚 API Documentation: http://localhost:8000/docs")
    print_bruno_examples()

def print_bruno_examples():
    print("\n📡 Bruno API Examples:")
    print("1. Upload Document:")
    print("   POST http://localhost:8000/upload-document")
    print("   Body: form-data with 'file' field")
    
    print("\n2. Generate Questions:")
    print("   POST http://localhost:8000/generate-questions/{document_id}")
    
    print("\n3. Search FAQ:")
    print("   POST http://localhost:8000/search-faq/{document_id}")
    print("   Body: {\"document_id\": \"your-doc-id\"}")
    
    print("\n4. Generate User Stories:")
    print("   POST http://localhost:8000/generate-user-stories/{document_id}")
    
    print("\n5. Complete Workflow:")
    print("   POST http://localhost:8000/process-brd-complete")
    print("   Body: form-data with 'file' field")

if __name__ == "__main__":
    main()
