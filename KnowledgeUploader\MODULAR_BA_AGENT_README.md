# Modular BA Agent API - Single Endpoint Solution

## 🎯 Overview

This is a **modular, single-endpoint** solution that processes Business Requirements Documents (BRDs) and generates user stories in one API call:

**Upload DOCX → Extract Text → Generate Questions → Search FAQ → Generate User Stories**

## 🏗️ Modular Architecture

### Core Modules

1. **`brd_processor.py`** - DOCX file processing and text extraction
2. **`question_generator.py`** - LLM-based question generation from BRD content
3. **`faq_searcher.py`** - Vector search for FAQ answers with your specific format
4. **`story_generator.py`** - User story generation from document + FAQ context
5. **`BA_Agent.py`** - Main FastAPI application with single endpoint

### Module Dependencies

```
BA_Agent.py
├── brd_processor.py (DOCX processing)
├── question_generator.py (LLM questions)
├── faq_searcher.py (Vector search)
├── story_generator.py (User stories)
└── qdrant_http_client.py (Vector DB)
```

## 🚀 Quick Start

### 1. Install Dependencies
```bash
cd KnowledgeUploader
pip install -r requirements.txt
```

### 2. Environment Setup
Ensure your `.env` file contains:
```env
# Azure OpenAI Configuration
AZURE_OPENAI_ENDPOINT_4_1=your_endpoint
AZURE_OPENAI_API_KEY_4_1=your_api_key
AZURE_OPENAI_VERSION_4_1=2025-01-01-preview

# Embedding Model Configuration
EMBEDDING_OPENAI_API_BASE=your_embedding_endpoint
EMBEDDING_OPENAI_API_KEY=your_embedding_api_key
EMBEDDING_OPENAI_API_VERSION=2023-05-15

# Qdrant Configuration
QDRANT_ENDPOINT=your_qdrant_url
QDRANT_API_KEY=your_qdrant_api_key
QDRANT_BRD_COLLECTION_NAME=your_faq_collection_name
```

### 3. Start the Server
```bash
python BA_Agent.py server
```

Server runs at: `http://localhost:8000`

## 📡 Single API Endpoint

### **POST /process-brd**

**Complete BRD processing in one call:**

```http
POST http://localhost:8000/process-brd
Content-Type: multipart/form-data

Body:
- file: your_brd_document.docx (required)
- collection_name: your_faq_collection (optional)
```

**Response:**
```json
{
  "success": true,
  "filename": "BRD-SD1.docx",
  "text_length": 15420,
  "questions_generated": 12,
  "faq_answers_found": 8,
  "epic": {
    "title": "Container Management System Enhancement",
    "description": "Epic for implementing container tracking...",
    "issues": [
      {
        "title": "Container Tracking System",
        "description": "As a depot manager, I want to track...",
        "acceptance_criteria": "Given I am logged in...",
        "priority": "High",
        "story_points": 8
      }
    ]
  },
  "processing_steps": [
    "Validating DOCX file",
    "Extracting text from DOCX",
    "Extracted 15420 characters",
    "Generating questions from document",
    "Generated 12 questions",
    "Searching FAQ database for answers",
    "Found 8 FAQ answers",
    "Generating user stories from combined context",
    "Generated epic with 5 user stories"
  ]
}
```

## 🔧 FAQ Format Support

The system is designed to work with your specific FAQ format:

```
Question 30 🏷️ Domain: UI/UX ❓ What are the current UI/UX challenges identified in SD1, and what improvements are planned? 💡 Answer ["There is no specific information provided..."]
```

The `faq_searcher.py` module automatically parses this format to extract clean answers.

## 🧪 Testing

### Run Test Suite
```bash
python test_single_endpoint.py
```

### Manual Testing with Bruno

**Single Endpoint Test:**
- **Method:** POST
- **URL:** `http://localhost:8000/process-brd`
- **Body:** Form-data with `file` field (upload your DOCX)
- **Optional:** Add `collection_name` field if needed

**Health Check:**
- **Method:** GET
- **URL:** `http://localhost:8000/health`

## 📊 Processing Flow

```
1. 📤 Upload DOCX File
   ↓
2. 📄 Extract Text Content
   ↓
3. ❓ Generate Questions (LLM)
   ↓
4. 🔍 Search FAQ Database (Vector Search)
   ↓
5. 📝 Generate User Stories (LLM with combined context)
   ↓
6. ✅ Return Complete Epic
```

## 🎯 Key Features

- ✅ **Single Endpoint** - Everything in one API call
- ✅ **Modular Design** - Clean separation of concerns
- ✅ **DOCX Processing** - Automatic text extraction
- ✅ **Smart Questions** - LLM generates relevant questions
- ✅ **FAQ Integration** - Searches your vector database
- ✅ **Format Parsing** - Handles your specific FAQ format
- ✅ **User Stories** - Complete JIRA epic generation
- ✅ **Error Handling** - Comprehensive error responses
- ✅ **Processing Steps** - Detailed workflow logging

## 🔍 Module Details

### BRDProcessor
- Validates DOCX files
- Extracts text from paragraphs and tables
- Handles temporary file management

### QuestionGenerator
- Uses LLM to analyze document content
- Generates 10-15 relevant questions
- Categorizes by domain (UI/UX, Security, etc.)
- JSON response parsing with fallbacks

### FAQSearcher
- Vector similarity search using embeddings
- Parses your specific FAQ format
- Extracts clean answers from complex format
- Returns relevance scores

### UserStoryGenerator
- Combines document + FAQ context
- Generates JIRA epic with user stories
- INVEST principle compliance
- Structured output with acceptance criteria

## 🚨 Error Handling

The system provides detailed error responses:

```json
{
  "success": false,
  "filename": "document.docx",
  "text_length": 0,
  "questions_generated": 0,
  "faq_answers_found": 0,
  "processing_steps": [
    "Validating DOCX file",
    "Extracting text from DOCX",
    "Error: LLM model not available - check Azure OpenAI configuration"
  ],
  "error": "LLM model not available - check Azure OpenAI configuration"
}
```

## 📚 API Documentation

Interactive documentation available at:
- **Swagger UI:** `http://localhost:8000/docs`
- **ReDoc:** `http://localhost:8000/redoc`

## 🎉 Success!

Your modular BA Agent is ready to process BRDs and generate comprehensive user stories with FAQ integration in a single API call! 🚀

**Bruno Request:**
```
POST http://localhost:8000/process-brd
Body: form-data with your DOCX file
```

That's it! Upload your BRD and get complete user stories with FAQ context! ✨
