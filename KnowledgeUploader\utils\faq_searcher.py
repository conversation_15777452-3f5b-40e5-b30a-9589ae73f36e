"""
FAQ Search Module
Searches FAQ database for answers to generated questions
"""

import logging
from typing import List
from pydantic import BaseModel

logger = logging.getLogger(__name__)

class FAQAnswer(BaseModel):
    question: str
    answer: str
    score: float

class FAQSearcher:
    """Searches FAQ database for answers to generated questions"""
    
    def __init__(self, qdrant_client, collection_name: str, embedding_model):
        """
        Initialize FAQ searcher
        
        Args:
            qdrant_client: QdrantHTTPClient instance
            collection_name: Name of the FAQ collection
            embedding_model: LangChain embedding model
        """
        self.client = qdrant_client
        self.collection_name = collection_name
        if not embedding_model:
            raise ValueError("Embedding model not available")
        self.embedding_model = embedding_model
    
    def search_faq_answers(self, questions: List) -> List[FAQAnswer]:
        """
        Search for FAQ answers to the generated questions
        
        Args:
            questions: List of GeneratedQuestion objects
            
        Returns:
            List of FAQAnswer objects
        """
        faq_answers = []
        
        for question in questions:
            try:
                question_vector = self.embedding_model.embed_query(question.question)
                
                search_results = self.client.search_points(
                    collection_name=self.collection_name,
                    query_vector=question_vector,
                    limit=3,
                    score_threshold=0.5
                )
                
                if 'result' in search_results:
                    points = search_results['result']
                    for point in points:
                        payload = point.get('payload', {})
                        content = payload.get('content', '')
                        score = point.get('score', 0)
                        
                        if content.strip():
                            parsed_faq = self._parse_faq_content(content)
                            if parsed_faq:
                                faq_answers.append(FAQAnswer(
                                    question=question.question,
                                    answer=parsed_faq['answer'],
                                    score=score
                                ))
                                break  
                            else:
                                faq_answers.append(FAQAnswer(
                                    question=question.question,
                                    answer=content[:500] + "..." if len(content) > 500 else content,
                                    score=score
                                ))
                                break
                                
            except Exception as e:
                logger.error(f"Error searching FAQ for question '{question.question}': {e}")
                continue
        
        logger.info(f"Found {len(faq_answers)} FAQ answers for {len(questions)} questions")
        return faq_answers
    
    def _parse_faq_content(self, content: str) -> dict:
        """
        Parse FAQ content in the format:
        Question 30 🏷️ Domain: UI/UX ❓ What are the current UI/UX challenges... 💡 Answer [...]
        
        Args:
            content: Raw FAQ content string
            
        Returns:
            Dictionary with parsed question and answer, or None if parsing fails
        """
        try:
            if '💡 Answer' in content:
                parts = content.split('💡 Answer')
                if len(parts) >= 2:
                    answer_part = parts[1].strip()
                    
                    if answer_part.startswith('[') and answer_part.endswith(']'):
                        answer_part = answer_part[1:-1]
                    elif answer_part.startswith('["') and answer_part.endswith('"]'):
                        answer_part = answer_part[2:-2]
                    
                    question_part = parts[0]
                    if '❓' in question_part:
                        question_text = question_part.split('❓')[-1].strip()
                    else:
                        question_text = "Related FAQ"
                    
                    return {
                        'question': question_text,
                        'answer': answer_part.strip()
                    }
            
            if 'Answer' in content and '[' in content:
                import re
                answer_match = re.search(r'\[(.*?)\]', content)
                if answer_match:
                    return {
                        'question': 'FAQ',
                        'answer': answer_match.group(1)
                    }
            
            return None
            
        except Exception as e:
            logger.error(f"Error parsing FAQ content: {e}")
            return None
