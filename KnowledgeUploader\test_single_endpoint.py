#!/usr/bin/env python3
"""
Test script for the Enhanced BA Agent API - Single Endpoint
Tests the complete workflow: Upload DOCX → Questions → FAQ Search → User Stories
"""

import requests
import json
import os
from pathlib import Path

# API base URL
BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test the health endpoint"""
    print("🔍 Testing Health Check...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health Check: {data}")
            return True
        else:
            print(f"❌ Health Check Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health Check Error: {e}")
        return False

def test_process_brd(file_path):
    """Test the single BRD processing endpoint"""
    print(f"🚀 Testing Complete BRD Processing: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    try:
        with open(file_path, 'rb') as f:
            files = {'file': (os.path.basename(file_path), f, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')}
            response = requests.post(f"{BASE_URL}/process-brd", files=files)
        
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                print(f"✅ BRD Processing Success!")
                print(f"   📄 Filename: {data['filename']}")
                print(f"   📊 Text Length: {data['text_length']} characters")
                print(f"   ❓ Questions Generated: {data['questions_generated']}")
                print(f"   🔍 FAQ Answers Found: {data['faq_answers_found']}")
                
                if data['epic']:
                    epic = data['epic']
                    print(f"   📝 Epic: {epic['title']}")
                    print(f"   📋 User Stories: {len(epic['issues'])}")
                    
                    # Show first story as example
                    if epic['issues']:
                        story = epic['issues'][0]
                        print(f"\n   📖 Sample Story:")
                        print(f"      Title: {story['title']}")
                        print(f"      Description: {story['description'][:100]}...")
                        print(f"      Priority: {story['priority']}, Points: {story['story_points']}")
                
                print(f"\n   🔄 Processing Steps:")
                for i, step in enumerate(data['processing_steps'], 1):
                    print(f"      {i}. {step}")
                
                return True
            else:
                print(f"❌ BRD Processing Failed: {data.get('error')}")
                if data.get('processing_steps'):
                    print("   Processing steps:")
                    for step in data['processing_steps']:
                        print(f"      - {step}")
                return False
        else:
            print(f"❌ BRD Processing Request Failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data}")
            except:
                print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ BRD Processing Error: {e}")
        return False

def test_collections():
    """Test the collections endpoint"""
    print("📚 Testing Collections Endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/collections")
        if response.status_code == 200:
            data = response.json()
            collections = data.get('result', {}).get('collections', [])
            print(f"✅ Collections Available: {[c['name'] for c in collections]}")
            return True
        else:
            print(f"❌ Collections Request Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Collections Error: {e}")
        return False

def main():
    print("🧪 Enhanced BA Agent API Test Suite - Single Endpoint")
    print("=" * 70)
    
    # Test health check
    if not test_health_check():
        print("❌ Health check failed. Make sure the server is running.")
        print("   Start server with: python BA_Agent.py server")
        return
    
    print("\n" + "=" * 70)
    
    # Test collections
    test_collections()
    
    print("\n" + "=" * 70)
    
    # Look for a sample DOCX file
    sample_files = [
        "sample_brd.docx",
        "../sample/materials/BRD-SD1.docx",
        "test_document.docx",
        "BRD-SD1.docx"
    ]
    
    test_file = None
    for file_path in sample_files:
        if os.path.exists(file_path):
            test_file = file_path
            break
    
    if not test_file:
        print("⚠️  No sample DOCX file found for testing.")
        print("   Expected files: sample_brd.docx, BRD-SD1.docx, test_document.docx")
        print("\n🔧 You can still test the endpoint manually with Bruno:")
        print_bruno_examples()
        return
    
    print(f"📄 Using test file: {test_file}")
    
    # Test complete BRD processing
    success = test_process_brd(test_file)
    
    print("\n" + "=" * 70)
    if success:
        print("✅ All tests completed successfully!")
    else:
        print("❌ Some tests failed. Check the logs above.")
    
    print("\n📚 API Documentation: http://localhost:8000/docs")
    print_bruno_examples()

def print_bruno_examples():
    print("\n📡 Bruno API Example:")
    print("POST http://localhost:8000/process-brd")
    print("Body: form-data with 'file' field (upload your DOCX)")
    print("\nOptional form field:")
    print("collection_name: your_faq_collection_name")
    print("\nResponse will include:")
    print("- Extracted text length")
    print("- Generated questions count")
    print("- FAQ answers found")
    print("- Complete epic with user stories")
    print("- Processing steps log")

if __name__ == "__main__":
    main()
