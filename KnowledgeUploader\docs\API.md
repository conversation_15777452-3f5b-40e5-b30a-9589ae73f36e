# Jarvis Knowledge API Documentation

## Overview

Jarvis Knowledge provides a single, powerful endpoint for processing Business Requirements Documents (BRDs) and generating comprehensive user stories with AI-powered analysis.

## Base URL

```
http://localhost:8000
```

## Authentication

Currently, no authentication is required. In production, implement appropriate authentication mechanisms.

## Endpoints

### POST /process-brd

Process a Business Requirements Document and generate user stories.

**Request:**
- Method: `POST`
- Content-Type: `multipart/form-data`
- Body: `file` (DOCX file)

**Response:**
```json
{
  "epic": {
    "title": "Container Management System Enhancement",
    "description": "Epic for implementing advanced container depot management features...",
    "issues": [
      {
        "title": "Reefer Temperature Monitoring System",
        "description": "As a depot manager, I want real-time reefer temperature monitoring...",
        "acceptance_criteria": "Given a reefer container is in the yard...",
        "priority": "High",
        "story_points": 8
      }
    ]
  }
}
```

**Error Responses:**
- `400 Bad Request`: Invalid file format or missing file
- `500 Internal Server Error`: Processing error

### GET /health

Check system health and status.

**Response:**
```json
{
  "status": "healthy",
  "qdrant_connected": true,
  "collections_available": ["faq_collection"],
  "timestamp": "2025-01-17T10:30:00Z"
}
```

### GET /docs

Interactive API documentation (Swagger UI).

### GET /collections

List available Qdrant collections.

**Response:**
```json
{
  "collections": [
    {
      "name": "faq_collection",
      "points_count": 1500,
      "status": "green"
    }
  ]
}
```

## Processing Flow

1. **Document Upload**: DOCX file is uploaded via multipart form
2. **Text Extraction**: Document content is extracted with structure preservation
3. **Question Generation**: AI generates domain-specific questions
4. **FAQ Search**: Vector search finds relevant FAQ answers
5. **Story Generation**: AI creates comprehensive user stories
6. **Response**: Structured epic with user stories returned

## Error Handling

All endpoints return appropriate HTTP status codes and error messages:

- `200 OK`: Successful processing
- `400 Bad Request`: Client error (invalid input)
- `422 Unprocessable Entity`: Validation error
- `500 Internal Server Error`: Server error

## Rate Limiting

Currently no rate limiting is implemented. Consider implementing rate limiting for production use.

## File Formats

**Supported Input Formats:**
- DOCX (Microsoft Word documents)

**Output Format:**
- JSON (structured epic with user stories)

## Configuration

See main README.md for environment variable configuration.
