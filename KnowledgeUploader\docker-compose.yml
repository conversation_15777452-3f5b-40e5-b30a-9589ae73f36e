version: '3.8'

services:
  ba-agent:
    build: .
    ports:
      - "8000:8000"
    environment:
      - HOST=0.0.0.0
      - PORT=8000
      - LOG_LEVEL=info
      # Azure OpenAI Configuration
      - AZURE_OPENAI_ENDPOINT_4_1=${AZURE_OPENAI_ENDPOINT_4_1}
      - AZURE_OPENAI_API_KEY_4_1=${AZURE_OPENAI_API_KEY_4_1}
      - AZURE_OPENAI_VERSION_4_1=${AZURE_OPENAI_VERSION_4_1}
      # Embedding Configuration
      - EMBEDDING_OPENAI_API_BASE=${EMBEDDING_OPENAI_API_BASE}
      - EMBEDDING_OPENAI_API_KEY=${EMBEDDING_OPENAI_API_KEY}
      - EMBEDDING_OPENAI_API_VERSION=${EMBEDDING_OPENAI_API_VERSION}
      # Qdrant Configuration
      - QDRANT_ENDPOINT=${QDRANT_ENDPOINT}
      - QDRANT_API_KEY=${QDRANT_API_KEY}
      - QDRANT_BRD_COLLECTION_NAME=${QDRANT_BRD_COLLECTION_NAME}
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
