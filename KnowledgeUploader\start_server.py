#!/usr/bin/env python3
"""
Container-friendly startup script for BA Agent API
"""

import os
import uvicorn
from BA_Agent import app

def start_server():
    """Start the BA Agent API server with container-friendly configuration"""
    
    # Get configuration from environment variables
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", 8000))
    workers = int(os.getenv("WORKERS", 1))
    log_level = os.getenv("LOG_LEVEL", "info")
    
    print(f"🚀 Starting BA Agent API server on {host}:{port}")
    print(f"📊 Workers: {workers}, Log Level: {log_level}")
    
    # Start the server
    uvicorn.run(
        "BA_Agent:app",
        host=host,
        port=port,
        workers=workers,
        log_level=log_level,
        access_log=True
    )

if __name__ == "__main__":
    start_server()
