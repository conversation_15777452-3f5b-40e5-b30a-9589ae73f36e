Document: BRD-SD1.docx
==================================================

costs, time, and reducing environmental impact. It is a key method for improving efficiency in container logistics.']

──────────────────────────────────────────────────

Question 30

🏷️ Domain: UI/UX ❓ What are the current UI/UX challenges identified in SD1, and what improvements are planned?

💡 Answer

["There is no specific information provided in the documents about the current UI/UX challenges identified in SD1 or the planned improvements. Without additional context or documentation, it is not possible to summarize the challenges or planned improvements for SD1's UI/UX."]

──────────────────────────────────────────────────

Question 31

🏷️ Domain: EDI Integrations

❓ What are the key EDI integrations required for SD1, and what systems are involved?

💡 Answer

of these critical areas and addressed gaps. Additionally, given the strong interdependence between bonded operations and yard management, we incorporated the missing bonded capabilities into this BRD. To assess the MVP version, we utilized a capability matrix [Appendix 2], conducting system walkthroughs and user interviews. In addition, we reapplied landside experience with OHP and discovered similar issues (handshake missing, amendments or incorrect cancellation etc). Based on the findings, we identified potential user stories and conducted follow-up interviews with volunteer site leaders to help prioritize them.

Additionally, during the yard management walkthrough, several consistency issues related to system design were discovered, prompting us to register a risk for further assessment.

This BRD aims to achieve three key objectives to support an effective rollout plan:

Evaluate the MVP yard management solution design and identify areas for architectural improvement.

Prioritize critical missing capabilities to be incorporated into the SD1 system before larger roll out

Align cross-platform initiatives and secure integration points, including: 

rld feedback.', '3. Localization: The Latin America version may have specific adaptations for local languages, regulations, or market needs, whereas the Chicago MVP is tailored for the US market.', '4 rld feedback.', '3. Localization: The Latin America version may have specific adaptations for local languages, regulations, or market needs, whereas the Chicago MVP is tailored for the US market.', '4 Purpose: The limited version may focus on technical validation or compliance, while the MVP aims to validate product-market fit and gather user feedback.', 'Without specific documents, these are the likely main differences based on standard deployment strategies.']

──────────────────────────────────────────────────

Question 3

🏷️ Domain: SD1

❓ What is the current architecture of the SD1 MVP version, and what are the proposed changes for modernization?

💡 Answer

Maersk Domain Knowledge Base FAQ

Generated on: July 11, 2025 at 08:33 PM

This document contains frequently asked questions and answers based on the Business Requirement Document analysis.

Table of Contents

g, potentially leading to address accuracy issues. While technologies such as IoT, voice-based input, and image recognition could address these challenges, they are currently considered out of scope   Additionally, for full-container heavy operation sites, such as India's CFS locations, optimizing container placement and loading schedules is a major challenge. However, this issue will be addressed at a later stage and is currently out of scope.

Problem Statement for this BRD

The MVP version of SD1 lacks several capabilities, and we have identified and prioritized the list of missing functional capabilities accordingly in section 5. 

However, before proceeding with their design and development, it is crucial to assess the current solution design to propose structural improvement (if required) to ensure it does not pose any blockers moving forward.  The rationale for the assessment is detailed in the Appendix 2.

Additionally, the absence of a well-structured training environment and comprehensive documentation hinders the ability to showcase existing YM capabilities effectively and conduct thorough discovery sessions.

ntext of EDI integr...
Q21: What does 'CMMS' stand for and how does it integrate with SD1 for equipment main...
Q22: What does 'MOS' stand for and what is its relevance to system and process adopti... ntext of EDI integr...
Q21: What does 'CMMS' stand for and how does it integrate with SD1 for equipment main...
Q22: What does 'MOS' stand for and what is its relevance to system and process adopti... Q23: What does 'SUS' stand for in the context of measuring efficiency or user satisfa...
Q24: What are 'depot archetypes' and how do they influence the rollout plan for SD1?
Q25: What are 'CFS locations' in India and what are their specific operational challe...
Q26: What are 'worklists' in the context of SD1 and how do they improve user experien...
Q27: How is the Google Maps API utilized in the SD1 YM codebase, and what are the cha...
Q28: Who is Baufest and what was their role in the development of the SD1 YM codebase...
Q29: What does 'triangulation' mean in the context of yard operations and how can it ...
Q30: What are the current UI/UX challenges identified in SD1, and what improvements a...
Q31: What are the key EDI integrations required for SD1, and what systems are involve...
Q32: What is the 'booking pre-confirmation' process and how is it currently managed v...
Q33: What is the 'appointment system' for truckers in SD1, and what are the challenge...

──────────────────────────────────────────────────

Question 16

🏷️ Domain: Reefer Operations

❓ What are 'reefer operations' and what capabilities are required for their management in SD1?

💡 Answer ['Reefer operations refer to the management and handling of refrigerated containers (reefers) used to transport temperature-sensitive goods such as food and pharmaceuticals. These operations involve ensuring that the correct temperature setpoints are maintained throughout the logistics chain, from origin to destination, and that any changes to temperature settings are properly managed and documented.', 'Key capabilities required for managing reefer operations in SD1 (likely a system or process context) include:', '1. Ability to update and communicate setpoint (temperature) changes at various stages (e.g., at origin terminal, on rail, on vessel, at transshipment, and at final port of discharge).', '2. Coordination with multiple stakeholders (shippers, packinghouses, cold storage, landside execution teams, truckers, terminal reefer vendors, vessel operators, and claims managers).', '3

 maintenance solution to integrate maintenance plan to yard activity plan and enable reach stack operator to claim ad hoc maintenance issues or observations.

Automation and less offline communication Implement real-time E-EIR (Electronic Equipment Interchange Receipt) integration with the shipping line to automate the retrieval of container status, key details, and planned actions. This will eliminate manual effort, reduce errors, and enhance operational efficiency within SD1. See below list of potential data points to be integrated:

Automating full container booking and delivery order process

Giving visibility to trucking company about booking pre-confirmation and equipment availability. Currently all this communication is happening via emails in MVP version. %80 cases trucking companies asking preconfirmation via email.

Roll Out Critical missing capabilities

Capabilities required for bonded depot operations with %100 system consistency and control

Capabilities required for full container reefer operations

A training YM environment with clean configuration where we can perform discovery sessions

Out of scope

tracking."]

──────────────────────────────────────────────────

Question 12

🏷️ Domain: E-EIR

❓ What does 'E-EIR' stand for and what is its function in the context of container operations?

💡 Answer ["'E-EIR' stands for 'Electronic Equipment Interchange Report'.", 'In the context of container operations, the eEIR app provides a digital platform for vendors to capture equipment interchange reports at depots and terminals whenever a container changes hands.', 'The app allows surveyors to inspect containers and record details such as container status, trucker information, and any damages (including photos).', 'Data entered into the app is stored in a database and a PDF report is generated and sent to relevant parties via email.', 'The eEIR app replaces the traditional paper-based method of capturing interchange reports, enabling global digital capture of EIR details.']

──────────────────────────────────────────────────

Question 13

🏷️ Domain: BCO SLA

❓ What does 'BCO SLA' mean and how does it impact yard planning and traffic management?

💡 Answer

ng equipment (e.g., reach stackers, forklifts) and any downtime logs and planned maintenance activities.
7) Safety Metrics: Key safety alerts, such as stacking height violations or incidents reported. ng equipment (e.g., reach stackers, forklifts) and any downtime logs and planned maintenance activities.
7) Safety Metrics: Key safety alerts, such as stacking height violations or incidents reported. 8) KPI Summary: Key performance indicators for yard efficiency, including container turn time, gate cycle time, and operational bottlenecks.
This cockpit should be interactive, allowing me to drill down into specific metrics, receive alerts for critical thresholds, and facilitate better decision-making to optimize daily operations. | P1

Yard Location Work List | I want to display the locations and applied yard planning rules on to this location set and change it easily | P1

Yard Traffic Control | As a Yard Supervisor, I want to have traffic direction logic & outdoor display connection  so that we can ensure safety and efficiency– applicable for large scale depots (multiple reach stackers and loading spots) | P1


Safety Risk Mitigation and visibility | As a Yard Supervisor, I want to get notified if a truck waits more than X minutes within the yard, so that I can follow up security and auditing purposes. | P1 Yard Cockpit Dashboard | As a Yard Supervisor, I want to have a yard cockpit view that provides a comprehensive real-time dashboard where I can monitor:
1) Daily Yard Utilization: Current capacity versus available space utilization
2) Total Gate Moves: Completed and pending gate operations 
3) Total Appointments / loading plan call off: Scheduled, completed, canceled, and no-show appointments for the day.
4)Depot Moves: Total container moves within the depot, categorized by type (storage, maintenance, inspection, etc.).
5) Operative Container Availability: Number of operationally available containers, segregated by size and type.
6) Equipment Availability: Status of yard handling equipment (e.g., reach stackers, forklifts) and any downtime logs and planned maintenance activities.
7) Safety Metrics: Key safety alerts, such as stacking height violations or incidents reported.

e/report value with automatic assignment (e.g. number of unproductive move or increased space utilization). Additional info: In MVP version automatic container gate out plan adopted only %18 of cases. Implement user-friendly worklists, enabling users to complete tasks with fewer screens and clicks. Focus on facilitating bulk updates, such as applying container restrictions and managing side processes like off-hire, sales, and pre-allocation workflows, to enhance efficiency and user experience. 

Traffic management and truck service scheduler (call off plan) for large scale depots (e.g. with 5 or more reach stackers and multiple loading spots) considering different criteria’s like number of unproductive moves, waiting time, safety, BCO SLA

Container Handling Equipment Availability: Connect YM with global equipment maintenance solution to integrate maintenance plan to yard activity plan and enable reach stack operator to claim ad hoc maintenance issues or observations.

Automation and less offline communication

─────────────────────────

Question 42

🏷️ Domain: Customs Systems Interface

❓ What is the interface between DMS and customs systems, and what data is exchanged for bonded depot compliance?

💡 Answer ["The interface between DMS (Depot Management System) and customs systems typically involves electronic data interchange (EDI) or API-based integration, allowing automated communication between the bonded depot's management software and the national customs authority's IT systems.", 'For bonded depot compliance, the data exchanged usually includes:', '- Inventory records (goods received, stored, and dispatched)', '- Customs declarations (import/export details, HS codes, values, etc.)', '- Movement authorizations (approvals for goods to enter/leave the bonded area)', '- Status updates (stock levels, compliance checks, audit trails)', '- Reference numbers (customs entry numbers, warehouse entry/exit references)', 'This interface ensures that all goods movements and storage within the bonded depot are reported in real-time or near real-time to customs, supporting compliance, auditability, and risk management.']

──────────────────────────────────────────────────

lly, the absence of a well-structured training environment and comprehensive documentation hinders the ability to showcase existing YM capabilities effectively and conduct thorough discovery sessions. Proposed Solution or Process Change (Owner can be FPO, BPO or PPO depending on the specific BRD)

The proposed solution will cover following capabilities

Integrated Safety Capability to yard management 

Bonded depot key functionalities

Enhancing digital capabilities of yard team via container and location worklist for bulk operations

Enhanced space/container allocation logic that will enable %100 adoption by operations team

Increased visibility for equipment reliability

Trucking vendor portal for booking pre-confirmation and appointments

Automated and enhanced booking process reducing offline communication

Enabling reefer operations capabilities like plug in tracking

or, I want to have traffic direction logic & outdoor display connection  so that we can ensure safety and efficiency– applicable for large scale depots (multiple reach stackers and loading spots) | P1 Bonded Depot Compliance | As a yard planner I want to tag and classify containers as bonded or non-bonded upon arrival, so I can ensure compliance with customs regulations. | P2

Bonded Depot Compliance | As a yard operator, I want a track documentation for customs clearance for bonded containers, so I can ensure proper documentation before movements. | P2

Bonded Depot Compliance | As a Yard Supervisor, I want to restrict user access rights for bonded depot operations so that only authorized personnel can perform specific actions and view sensitive data. | P2

Bonded Depot Compliance | As a Yard Supervisor, I want to generate yard reports tailored to specific customs requirements (to be defined for each country) so that I can meet compliance standards efficiently. | P2

Bonded Depot Compliance | As a Yard Supervisor I want DMS has an interface with customs systems to automate data exchange and improve the accuracy and efficiency of customs clearance processes. | P2

rs or rate limits.']

──────────────────────────────────────────────────

Question 28

🏷️ Domain: Baufest

❓ Who is Baufest and what was their role in the development of the SD1 YM codebase?

💡 Answer ['Baufest is a company, likely involved in software development or IT services. Based on the query, Baufest had a role in the development of the SD1 YM codebase. However, there are no specific documents provided to detail their exact contributions or responsibilities. Therefore, it can be summarized that Baufest participated in the development of the SD1 YM codebase, but further details about their specific role are not available from the provided information.']

──────────────────────────────────────────────────

Question 29

🏷️ Domain: Triangulation

❓ What does 'triangulation' mean in the context of yard operations and how can it optimize container moves?

💡 Answer

pany processes, and potential technical issues requiring support. The document does not mention specific adoption challenges for SD1.']

──────────────────────────────────────────────────

Question 34 🏷️ Domain: Depot Layout Overview

❓ What is the 'depot layout overview' feature in SD1 and how does it support yard planning?

💡 Answer

["The 'depot layout overview' feature in SD1 (presumably referring to a software or system related to depot or yard management) provides a visual or schematic representation of the depot or yard layout. This feature helps users understand the spatial arrangement of tracks, platforms, storage areas, and other key infrastructure within the depot.", "By offering a clear overview of the depot's physical layout, this feature supports yard planning by enabling planners to efficiently allocate space, schedule train or vehicle movements, and optimize the use of available resources. It helps in identifying potential bottlenecks, planning maintenance activities, and ensuring smooth operations within the yard."]

──────────────────────────────────────────────────

Question 35

🏷️ Domain: Bulk (Heap) Depot Location

anual errors and slow processes.', '4. Regulatory and compliance issues, including frequent changes in customs procedures.', '5. High operational costs due to land, labor, and equipment expenses.', '6 anual errors and slow processes.', '4. Regulatory and compliance issues, including frequent changes in customs procedures.', '5. High operational costs due to land, labor, and equipment expenses.', '6 Competition from Direct Port Delivery (DPD) and Direct Port Entry (DPE) models, which bypass CFSs for faster cargo movement.']

──────────────────────────────────────────────────

Question 26

🏷️ Domain: Worklists

❓ What are 'worklists' in the context of SD1 and how do they improve user experience and operational efficiency?

💡 Answer



Bonded Depot Compliance | As a Yard Supervisor I want DMS has an interface with customs systems to automate data exchange and improve the accuracy and efficiency of customs clearance processes. | P2 Bonded Depot Compliance | As a Yard Operator, I want the system to validate a container's customs clearance status before initiating a gate-out process, so that only containers with the correct status are allowed to leave the bonded depot. | P2

Bonded Depot Compliance | As a Yard Planner, I want the system to enforce container placement rules based on bonded area restrictions, so that containers are only located in zones authorized for their customs clearance or bonded status. | P2

Bonded Depot Inspection | As a yard operator, I want to plan and execute bonded container inspection orders and track their status (including seal change), so I can ensure compliance with customs requirements without delays. | P2

Bonded Depot Inspection | As a Yard Supervisor I want to track bonded container inspection moves to outside of depot so that I have visibility into the container's status and location. | P2

❓ What is the 'appointment system' for truckers in SD1, and what are the challenges in its adoption?

💡 Answer ["The 'appointment system' for truckers in SD1 (specifically Port Elizabeth) is managed through the Queue Management System (QMS). Trucking companies set up appointments in TERMPoint, and Port Elizabeth clerks use QMS to approve these appointments. The system is designed to manage and streamline truck appointments at the port.", 'The Service Desk (SD) supports this system by creating tickets and assigning them to the appropriate resolver group (APMT QMS PEB - TCS - L3) for issue resolution. There are dedicated ServiceNow links for accessing the QMS and related services.', 'Challenges in adoption are not explicitly detailed in the provided document. However, common challenges in such systems typically include user adaptation to new digital workflows, integration with existing trucking company processes, and potential technical issues requiring support. The document does not mention specific adoption challenges for SD1.']

──────────────────────────────────────────────────

Question 34

──────────────────────────────────────────────────

Question 25

🏷️ Domain: CFS Locations

❓ What are 'CFS locations' in India and what are their specific operational challenges?

💡 Answer ['CFS stands for Container Freight Station. In India, CFS locations are facilities near ports or inland where imported/exported containers are temporarily stored, examined, and cleared by customs before being moved to their final destination or loaded onto ships.', 'CFSs play a crucial role in decongesting ports, facilitating customs clearance, and providing value-added services like stuffing, de-stuffing, and warehousing.', 'Operational challenges faced by CFS locations in India include:', '1. Congestion and delays due to high container volumes and limited infrastructure.', '2. Inefficient coordination between port authorities, customs, and transporters.', '3. Inadequate use of technology for tracking and documentation, leading to manual errors and slow processes.', '4. Regulatory and compliance issues, including frequent changes in customs procedures.', '5. High operational costs due to land, labor, and equipment expenses.', '6

Business Requirements Document (BRD)

Objective or Background or Introduction of the Early/PRFAQ

The SD1 system currently exists in two versions: A limited version, deployed across nine sites in Latin America (LAM), covering empty container operations such as booking, gate in/out, and inspections. And a newer version, implemented as an MVP in Chicago (NAM), offering additional capabilities, including yard management, equipment maintenance and repair (EMR), full container and chassis operations.

As we focus on refactoring the MVP version into a modernized architecture, the key question arises: Is it ready for a broader rollout?

We initially identified gaps in advanced capabilities such as bonded operations, stuffing/destuffing, and billing extraction, which were already included in our OP plan. However, following feedback from the latest OP call, we were directed to assess how well the MVP addresses fundamental pain points at depot sites, including safety.

────────────────────────────────────────────────

Question 38

🏷️ Domain: Container Address Accuracy Control

❓ What is 'container address accuracy control' and how is it implemented in SD1?

💡 Answer ["'Container address accuracy control' refers to the process or system used to ensure that the addresses assigned to containers (such as shipping containers, data containers, or network containers) are correct and accurate. This is important for tracking, delivery, and management purposes.", 'In the context of SD1 (which could refer to a specific standard, system, or software, but is not further defined in the provided information), the implementation details are not specified in the available documents. Generally, such control is implemented through validation checks, address verification systems, and possibly automated scanning or data entry systems to minimize errors.', "Without more specific information about SD1, a general answer is that container address accuracy control is implemented by verifying and validating address data at various stages of the container's lifecycle, using both manual and automated processes."]

──────────────────────────────────────────────────

ays. | P2

Bonded Depot Inspection | As a Yard Supervisor I want to track bonded container inspection moves to outside of depot so that I have visibility into the container's status and location. | P2 Booking Management | As a Yard Planner, I want to get a report if customer do not follow the booking instruction and pick up container from other depots so that I can claim this to equipment team and secure the volume. | P2

Booking Management | As a Yard Planner, I want trucking companies have access to a portal where they see their assigned booking and container stock availability and further instructions so that we avoid offline communication | P2

Chassis Operations | As a Yard Operator, I want YM tracks chassis locations in the yard and apply special restrictions and rules so that we can manage chassis yard operations | P2

EDI Integrations | Booking amendments after gate out needs to be automatically updated in the system and send to RKEM without manual intervention | P2

rity.']

──────────────────────────────────────────────────

Question 4

🏷️ Domain: Yard Management (YM)

❓ What are the core functionalities of the Yard Management (YM) solution within SD1?

💡 Answer ['The core functionalities of the Yard Management (YM) solution within SD1, as described for port terminal operations, include:', '1. Traffic Management: Controls and manages the flow of vehicles (trucks, trains, etc.) within the terminal to prevent congestion and optimize transportation routes.', '2. Environmental Monitoring: Assesses environmental conditions such as air quality, noise, and waste management to ensure compliance with regulations.', '3. Inventory Control: Monitors the status and location of containers and cargo, aiding in inventory management and loss prevention.', '4. Communication and Coordination: Facilitates communication between internal departments and external stakeholders (e.g., customs, shipping companies).', '5

 the yard.']

──────────────────────────────────────────────────

Question 11

🏷️ Domain: Global EHS Solution

❓ What is the 'Global EHS solution' and how does it provide safety data to SD1?

💡 Answer ["The 'Global EHS Solution' refers to a system or platform designed to manage Environmental, Health, and Safety (EHS) information on a global scale for organizations. It typically centralizes EHS data, compliance requirements, incident reporting, and safety documentation.", "While the specific details about how it provides safety data to 'SD1' are not available in the provided documents, generally, such solutions integrate with other systems (like SD1, which may be a Safety Data or SAP module) via data interfaces, APIs, or automated reporting. This allows the transfer of safety-related data (such as incident logs, compliance status, or safety data sheets) from the Global EHS Solution to SD1 for further processing, analysis, or compliance tracking."]

──────────────────────────────────────────────────

Question 12

🏷️ Domain: E-EIR

❓ What does 'E-EIR' stand for and what is its function in the context of container operations?

💡 Answer

omain Knowledge Base FAQ

Generated on: July 11, 2025 at 08:33 PM

This document contains frequently asked questions and answers based on the Business Requirement Document analysis.

Table of Contents Q1: What does SD1 stand for in the context of Maersk's operations?
Q2: What are the main differences between the limited version of SD1 deployed in Lat...
Q3: What is the current architecture of the SD1 MVP version, and what are the propos...
Q4: What are the core functionalities of the Yard Management (YM) solution within SD...
Q5: What are 'bonded operations' in the context of depot management, and what specif...
Q6: What do 'stuffing' and 'destuffing' refer to in depot operations, and why are th...
Q7: What does 'OP plan' and 'OP call' refer to in this context?
Q8: What is 'OHP' and how does it relate to landside experience in Maersk's operatio...
Q9: What is the 'Asset management solution (e-EIR)' and how does it integrate with S...
Q10: What is the 'Equipment maintenance solution (CMMS)' and what role does it play i...
Q11: What is the 'Global EHS solution' and how does it provide safety data to SD1?

──────────────────

Question 32

🏷️ Domain: Booking Pre-confirmation

❓ What is the 'booking pre-confirmation' process and how is it currently managed versus the proposed automated approach?

💡 Answer ["The 'booking pre-confirmation' process involves vendors submitting bookings through three main channels: (1) DEX - via the Damco shipper portal, (2) GBI - via a third-party site which triggers the booking to MODS, and (3) MOD - via email, where the GSC team manually creates the booking in MODS. For DEX and GBI, the booking details provided by the vendor must be validated against the client's purchase order. If there are no discrepancies, the booking is confirmed; if discrepancies are found, a discrepancy report is raised to SCM customer service.", 'Currently, this process is managed manually, especially for MOD bookings, and requires validation and confirmation actions by staff. The proposed automated approach aims to confirm bookings received from all sources within a 2-hour SLA, reducing manual intervention, speeding up the process, and ensuring timely confirmations.']

──────────────────────────────────────────────────

Question 33

🏷️ Domain: Appointment System

cold cargo | P0

Safety Risk Mitigation and visibility | As a Yard Supervisor, I want to be able to close specific depot area for operations due to risk, so that we mitigate any potential hazard. | P0 Safety Risk Mitigation and visibility | As a Yard Supervisor, I want to be able to define daily/shiftly risk level in cases like weather condition, equipment repair in the yard,  there are visitors , so that I can set the operations team alerted and postpone some non critical activities in case of high risk. | P0

Yard Container Work List | As a EMR lead, I want to view a list of containers with following details  
a) pending for inspection
b) flagged for damage and located in the yard, so that I can plan for estimate creation.
c) estimate status : to be created / pending for approval and approved for repair
and from the same worklist I could take following actions : 
e) create container move request among locations
f) trigger an ad hoc yard inspection and add damage and observation | P0

re already included in our OP plan. However, following feedback from the latest OP call, we were directed to assess how well the MVP addresses fundamental pain points at depot sites, including safety. To address this, we conducted a survey among 36 depot site leaders in November 2024 [Appendix 1] to rank their top operational challenges. From the highest-ranked issues we picked up following items:

Yard Management

Safety

Unnecessary Communications [Offline communication via emails/phones mostly due to lack of automation and visibility]

Then, we evaluated the MVP’s coverage of these critical areas and addressed gaps. Additionally, given the strong interdependence between bonded operations and yard management, we incorporated the missing bonded capabilities into this BRD.

──────────────────────────────────────────

Question 29

🏷️ Domain: Triangulation

❓ What does 'triangulation' mean in the context of yard operations and how can it optimize container moves?

💡 Answer ["In the context of yard operations, 'triangulation' refers to the process of linking an empty container from an Import Roundtrip with an Export Roundtrip booking, especially when the export pickup is at or near the import delivery point. This avoids the need to return the empty container to the depot or port, thereby reducing unnecessary empty container moves.", "Triangulation is only possible for truck moves

 leaders. Additionally for some yard activities global functions like EHS, equipment maintenance are proposing their vertical solutions which may cause bad user experience of dealing with many systems BRD for Initiative(s) | SD1 New Capabilities-1 | [Insert link(s) to PRFAQ(s) or Early FAQ(s)]

BRD version # | [1.0]

Last update | [23-01-2025]

Author (PPO) | Ozcan Kinali

BPO | Nadja Herfurth / Wendy Bal

SFPO | Felipe Gustavo

Review group | Platform team

Approval status | [Not Approved/Approved]

Capability | User Story | Priority

EDI Integrations | As a Yard Supervisor, I want DMS has  Maersk equipment database system (e-EIR or AEMS) integration to validate container to be gated out or gated in against latest equipment status | P0

Reefer & Genset  Operation Control | As a Yard Planner, I want system gets reefer instruction from the booking and assign as work order so that we keep required temperature and humidity for cold cargo | P0

Safety Risk Mitigation and visibility | As a Yard Supervisor, I want to be able to close specific depot area for operations due to risk, so that we mitigate any potential hazard. | P0

tent identity and authentication via the WRB Azure Hub.', '- There is ongoing assessment of the required level of connectivity between regions and between the AWS LF Logistics Landing Zone and the AWS tent identity and authentication via the WRB Azure Hub.', '- There is ongoing assessment of the required level of connectivity between regions and between the AWS LF Logistics Landing Zone and the AWS Maersk Landing Zone, with a focus on minimizing unnecessary access and maintaining security.']

──────────────────────────────────────────────────

Question 4

🏷️ Domain: Yard Management (YM)

❓ What are the core functionalities of the Yard Management (YM) solution within SD1?

💡 Answer

ll adopted functionality in landside TMS].

The mock-up above illustrates one of the worklists we aim to deliver:

Requirement statements (PPO driven)

Success criteria, Metrics, and KPIs (PPO driven) The success of the enhancements will be measured by:
- Adoption of automatic system allocation logic (target %90)
- % of manually created bookings (target %5)
- Reduction in unproductive yard moves (target %20 improvement)
- Decrease in safety incidents and risks (target and measurement logic TBD)
- Decrease in vendor emails for preconfirmation (TBD)
- Increased efficiency in space utilization and container dwell time. (TBD)
- Increased SUS (expected progress is move from 50 to 55)

Open Items & Risk Mitigation 

Legal/Regulatory / N&C - if relevant (PPO driven)

Compliance with local and international safety standards, particularly in stacking heights, yard restrictions and safety.

Business policies (FPO driven) – if relevant

Yard management and safety process maps [if exist] to be added here   

(Appendix) Functional Sign-offs and other artefacts, appended to the BRD (Mandatory to evaluate)

 
Appendix 1 – Survey result for operational challenges

ing accessorial and miscellaneous charges), and tracking invoice/payment status. These systems facilitate collaboration between Maersk and trucking vendors throughout the inland transport lifecycle.'] ──────────────────────────────────────────────────

Question 16

🏷️ Domain: Reefer Operations

❓ What are 'reefer operations' and what capabilities are required for their management in SD1?

💡 Answer

────────────────────────

Question 2

🏷️ Domain: SD1

❓ What are the main differences between the limited version of SD1 deployed in Latin America and the MVP version implemented in Chicago?

💡 Answer ["There is no direct documentation provided, but based on typical software deployment practices, the main differences between a 'limited version' and an 'MVP (Minimum Viable Product) version' often include:", '1. Feature Set: The limited version in Latin America likely has fewer features or restricted functionality compared to the MVP in Chicago, which may include the core set of features intended for initial market validation.', '2. User Base: The limited version may be targeted at a smaller, controlled group of users for testing, while the MVP in Chicago could be available to a broader audience for real-world feedback.', '3. Localization: The Latin America version may have specific adaptations for local languages, regulations, or market needs, whereas the Chicago MVP is tailored for the US market.', '4

ucking vendor portal for booking pre-confirmation and appointments

Automated and enhanced booking process reducing offline communication

Enabling reefer operations capabilities like plug in tracking An incremental delivery approach will be followed based on roll out plan and depot archetypes. However, realizing the full value of these capabilities depends on effective adoption and robust change management. Additionally, certain processes (e.g. order handling) will require revision and reinforcement to align with the new system and ensure seamless integration into daily operations. 

Additionally, the use and adoption of appointment system by truckers significantly enhance visibility into upcoming demand. While SD1 offers this capability, its implementation may face resistance in certain locations. For example, Chicago did not show interest where vendors were reluctant to comply. Successfully onboarding both the appointment process and the associated SD1 capability is therefore a critical enabler for improving yard planning efficiency.

Overview of the customer experience

There are primarily 4 personas that will see the impact of YM implementation on a regular basis

 and resource utilization.', "Without more context or documentation, it is not possible to provide details on how the 'yard task scheduler' specifically optimizes task assignment and prioritization."] ──────────────────────────────────────────────────

Question 42

🏷️ Domain: Customs Systems Interface

❓ What is the interface between DMS and customs systems, and what data is exchanged for bonded depot compliance?

💡 Answer

f EIR details.']

──────────────────────────────────────────────────

Question 13

🏷️ Domain: BCO SLA

❓ What does 'BCO SLA' mean and how does it impact yard planning and traffic management?

💡 Answer ["'BCO SLA' stands for 'Beneficial Cargo Owner Service Level Agreement.'", 'A BCO is a company that owns the cargo being shipped, as opposed to intermediaries like freight forwarders or NVOCCs.', 'An SLA (Service Level Agreement) is a contract that defines the expected level of service between parties, such as performance metrics, timelines, and responsibilities.', 'In the context of yard planning and traffic management, a BCO SLA would specify requirements for how cargo owned by the BCO should be handled, including turnaround times, priority handling, and other operational standards.', 'This impacts yard planning by requiring resources to be allocated to meet the agreed service levels for BCO cargo, potentially prioritizing their containers or shipments over others.', 'For traffic management, it may mean scheduling truck arrivals and departures to meet SLA commitments, reducing congestion, and ensuring timely movement of BCO cargo through the facility.']

─────────────────────────────────────────

Question 26

🏷️ Domain: Worklists

❓ What are 'worklists' in the context of SD1 and how do they improve user experience and operational efficiency?

💡 Answer ["In the context of SD1 (which often refers to Service Desk or Service Delivery systems), 'worklists' are organized lists or queues of tasks, tickets, or actions assigned to users or teams. They help users prioritize and manage their workload by presenting actionable items in a clear, structured manner.", 'Worklists improve user experience by providing clarity on what needs to be done, reducing confusion, and enabling users to focus on high-priority tasks. They also enhance operational efficiency by streamlining task assignment, tracking progress, and ensuring that nothing is overlooked, leading to faster resolution times and better resource management.']

──────────────────────────────────────────────────

Question 27

🏷️ Domain: Google Maps API

❓ How is the Google Maps API utilized in the SD1 YM codebase, and what are the challenges associated with its use?

💡 Answer

─────────────────────────────────────────────

Question 9

🏷️ Domain: Asset Management Solution (e-EIR)

❓ What is the 'Asset management solution (e-EIR)' and how does it integrate with SD1?

💡 Answer ["The 'Asset Management Solution (e-EIR)' is a digital application used by Maersk to capture Equipment Interchange Reports (EIR) at depots and terminals whenever a container changes hands. It allows surveyors to inspect containers and record details such as container status, trucker information, and any damages, including photos. The data is manually entered by users and stored in an Azure database. A PDF report is generated and sent via email to relevant parties, including the shop, trucker, and DERT. Previously, these reports were captured on paper; the eEIR app digitizes this process for global use. The available information does not specify direct integration details with SD1, so no information about integration with SD1 is provided in the document."]

──────────────────────────────────────────────────

Question 10

🏷️ Domain: Equipment Maintenance Solution (CMMS)

❓ What is the 'Equipment maintenance solution (CMMS)' and what role does it play in yard management?

💡 Answer

ds of each group rather than treating all depots the same. This can help in prioritizing which depots to roll out to first, allocating resources appropriately, and anticipating potential challenges.'] ──────────────────────────────────────────────────

Question 25

🏷️ Domain: CFS Locations

❓ What are 'CFS locations' in India and what are their specific operational challenges?

💡 Answer

hat bulk (heap) depot locations are for loose, unorganized storage, while stack-based logic is for organized, trackable storage of discrete items.']

────────────────────────────────────────────────── Question 36

🏷️ Domain: Container Restriction Reason

❓ What is a 'container restriction reason' and how is it managed in SD1?

💡 Answer

─────────────────────────

Question 10

🏷️ Domain: Equipment Maintenance Solution (CMMS)

❓ What is the 'Equipment maintenance solution (CMMS)' and what role does it play in yard management?

💡 Answer ['An Equipment Maintenance Solution, often referred to as a Computerized Maintenance Management System (CMMS), is a software platform designed to help organizations manage, track, and optimize the maintenance of their equipment and assets.', 'In the context of yard management, a CMMS plays a crucial role by ensuring that all equipment (such as vehicles, machinery, and handling tools) used in the yard is properly maintained, scheduled for regular inspections, and quickly repaired when issues arise. This helps reduce equipment downtime, improve safety, extend asset life, and enhance overall operational efficiency in the yard.']

──────────────────────────────────────────────────

Question 11

🏷️ Domain: Global EHS Solution

❓ What is the 'Global EHS solution' and how does it provide safety data to SD1?

💡 Answer

Booking Management | As a Yard Planner, I want to be able to reactivate cancelled bookings in specific cases with ability of attaching relevant audit proof so that I avoid manual booking creation | P1 Container address accuracy control | As a Yard Supervisor, I want to implement a digital location audit tool for inventory accuracy, so that I can ensure system data accuracy is ensured. | P1

Safety Risk Mitigation and visibility | As a Yard Supervisor, I want to get notified if a truck waits more than X minutes within the yard, so that I can follow up security and auditing purposes. | P1

100 system consistency and control

Capabilities required for full container reefer operations

A training YM environment with clean configuration where we can perform discovery sessions

Out of scope Workforce management, Billing extract, stuffing destuffing and gate activities specific to bonded depot are out of scope and will be covered in separate BRD’s

Bonded depot yard functionalities specific to Peru site are not included. Those will be part of Peru roll out BRD which will be delivered following the on-site discovery session.

Usability improvements are kept limited at providing handy tools to manage containers, locations and work orders. There are other known UI issues that will be addressed in an upcoming BRD with the help of UI/UX lead.  

For reach truck operators, synchronizing container movements with system updates can be challenging, potentially leading to address accuracy issues. While technologies such as IoT, voice-based input, and image recognition could address these challenges, they are currently considered out of scope  

t and accountability."]

──────────────────────────────────────────────────

Question 8

🏷️ Domain: OHP

❓ What is 'OHP' and how does it relate to landside experience in Maersk's operations?

💡 Answer ["'OHP' in the context of Maersk's operations typically stands for 'OneHUB Platform.' This is a digital platform or system used by Maersk to streamline and manage landside logistics operations, such as container handling, trucking, and terminal activities. The OHP aims to improve the landside experience by providing better visibility, coordination, and efficiency for cargo movements outside the port (i.e., on land).", 'By integrating various landside processes and stakeholders (like truckers, warehouses, and customs), OHP helps Maersk offer smoother, more reliable, and transparent services to its customers. This enhances the overall landside experience by reducing delays, improving communication, and optimizing logistics workflows.']

──────────────────────────────────────────────────

Question 9

🏷️ Domain: Asset Management Solution (e-EIR)

❓ What is the 'Asset management solution (e-EIR)' and how does it integrate with SD1?

💡 Answer

and improve efficiency.']

──────────────────────────────────────────────────

Question 18

🏷️ Domain: AEMS

❓ What does 'AEMS' stand for and how does it relate to e-EIR and DMS integration?

💡 Answer ["AEMS typically stands for 'Asset and Equipment Management System.'", 'In the context of e-EIR (electronic Equipment Issue/Return) and DMS (Document Management System) integration, AEMS would serve as the central system for tracking and managing assets and equipment.', 'e-EIR is likely a module or system for electronically recording the issuance and return of equipment, which would update records in AEMS.', 'DMS integration would allow documents related to assets (such as manuals, certificates, or maintenance records) to be linked or stored alongside asset records in AEMS.', 'Thus, AEMS acts as the core repository, while e-EIR and DMS provide specialized functionalities that integrate with it to streamline asset management and documentation.']

──────────────────────────────────────────────────

Question 19

🏷️ Domain: DMS

❓ What does 'DMS' stand for in this context and what is its role in yard management?

💡 Answer

─────────────────────────────────────────────────

Question 3

🏷️ Domain: SD1

❓ What is the current architecture of the SD1 MVP version, and what are the proposed changes for modernization?

💡 Answer ['The current architecture of the SD1 MVP version (as described in the context of LF Logistics and Maersk) involves the following key elements:', '- A dedicated datacenter or AWS account is created to support the MVP requirements, ensuring isolation and control for the initial deployment.', '- Networking connectivity is established back to the WRB Azure Hub, allowing for the provisioning of WRB Domain Controllers to handle identity and authentication for the MVP.', "- The MVP zone is designed to be independent of the AWS Maersk Landing Zone, meaning it can be built and operated without dependencies on Maersk's broader AWS infrastructure.", '- IAM provisioning is planned to facilitate user creation in WRB, with different provisioning models for LFL and existing Maersk users.', '- MVP computers are joined to the WRB Domain, and the architecture is designed to be replicable for future regional expansion, with each region connecting to the WRB Azure Hub but otherwise remaining

 that we can manage chassis yard operations | P2

EDI Integrations | Booking amendments after gate out needs to be automatically updated in the system and send to RKEM without manual intervention | P2 Safety Risk Mitigation and visibility | As a yard/ RS operator, I want to report a real-time incident/observation which will be integrated to global EHS solution, so that I do not use multiple system while working in the yard | P2

Yard Equipment Maintenance | As a Yard Supervisor, I want YM is integrated with global maintenance system (CMMS) and allows reach stacker to claim some observation and issues from DMS to CMMS so that we accelerate the info flow | P2

Yard Equipment Maintenance | As a Yard Supervisor, I want to display some information from CMMS in yard management dashboard like fuel consumption per reach stacker  so that we create visibility within DMS and build some cross reports | P2

Yard History Report | As a Yard Planner, I want to analyze historical yard activity trend and waiting times, customer SLA, so that I can identify bottlenecks, rush hours and improvement opportunities | P2

 does it integrate with S...
Q10: What is the 'Equipment maintenance solution (CMMS)' and what role does it play i...
Q11: What is the 'Global EHS solution' and how does it provide safety data to SD1?  does it integrate with S...
Q10: What is the 'Equipment maintenance solution (CMMS)' and what role does it play i...
Q11: What is the 'Global EHS solution' and how does it provide safety data to SD1? Q12: What does 'E-EIR' stand for and what is its function in the context of container...
Q13: What does 'BCO SLA' mean and how does it impact yard planning and traffic manage...
Q14: What are 'reach stackers' and what is their role in large scale depot operations...
Q15: What is the 'trucking vendor portal' and what functionalities are expected from ...
Q16: What are 'reefer operations' and what capabilities are required for their manage...
Q17: What are 'chassis operations' and how are they managed within the yard managemen...
Q18: What does 'AEMS' stand for and how does it relate to e-EIR and DMS integration?
Q19: What does 'DMS' stand for in this context and what is its role in yard managemen...
Q20: What does 'RKEM' stand for and what is its function in the context of EDI integr...
Q21: What does 'CMMS' stand for and how does it integrate with SD1 for equipment main...
Q22: What does 'MOS' stand for and what is its relevance to system and process adopti...

s. | P2

Risk | Mitigation Plan | Owner

System design limitation for enhancements due to reasons listed in Appendix 2 | Perform a deep design assessment and enhancement easiness | Engineering Manager Integrity challenges: Ensuring compatibility with existing SD1 modules | Perform a deep design assessment and enhancement easiness | Engineering Manager

Data quality: Real-time location and movement data must be accurate. | Daily check and analysis must be integrated MOS | FPO/BCM

System adoption for automatic planning (location and container allocation to bookings). | Daily check and analysis must be integrated MOS
Proper KPI must be available to display adoption | FPO / PPO/BCM

Process adoption for preconfirmation tool be truckers | Effective change management | BCM

uipment tracking and validation for other business processes.', "RKEM is a core system in Maersk's IT landscape, integrating with booking, documentation, vessel scheduling, and product catalog systems uipment tracking and validation for other business processes.', "RKEM is a core system in Maersk's IT landscape, integrating with booking, documentation, vessel scheduling, and product catalog systems It ensures data consistency and validation (e.g., vessel/voyage/port validation) and supports business operations by providing accurate, up-to-date equipment information through EDI and other interfaces."]

──────────────────────────────────────────────────

Question 21

🏷️ Domain: CMMS

❓ What does 'CMMS' stand for and how does it integrate with SD1 for equipment maintenance?

💡 Answer

 2-hour SLA, reducing manual intervention, speeding up the process, and ensuring timely confirmations.']

──────────────────────────────────────────────────

Question 33

🏷️ Domain: Appointment System ❓ What is the 'appointment system' for truckers in SD1, and what are the challenges in its adoption?

💡 Answer

Question 39

🏷️ Domain: Yard Cockpit Dashboard

❓ What is the 'yard cockpit dashboard' and what key metrics and functionalities does it provide?

💡 Answer ["The 'yard cockpit dashboard' is a centralized monitoring and management system used in port terminals (such as APM Terminals Quetzal) to oversee and optimize various aspects of yard and terminal operations.", 'Key metrics and functionalities provided by the yard cockpit dashboard include:', '- Traffic management: Controls and manages the flow of trucks, trains, and other vehicles within the terminal to prevent congestion and optimize transportation routes.', '- Environmental monitoring: Tracks air quality, noise levels, and waste management to ensure compliance with environmental regulations.', '- Inventory control: Monitors the status and location of containers and cargo, helping to manage inventories and prevent loss or damage.', '- Communication and coordination: Facilitates communication between terminal departments and external parties (e.g., customs, shipping companies).', '- Analysis and reporting: Generates data and reports on operational efficiency, equipment performance,

 others.', 'For traffic management, it may mean scheduling truck arrivals and departures to meet SLA commitments, reducing congestion, and ensuring timely movement of BCO cargo through the facility.'] ──────────────────────────────────────────────────

Question 14

🏷️ Domain: Reach Stackers

❓ What are 'reach stackers' and what is their role in large scale depot operations?

💡 Answer

['Reach stackers are specialized vehicles used for handling intermodal cargo containers in ports, rail yards, and large-scale depot operations.', 'They are equipped with a telescopic boom and a spreader, allowing them to lift, move, and stack containers efficiently, often up to several containers high and deep.', 'In large-scale depot operations, reach stackers play a crucial role in quickly moving containers between storage areas, trucks, and trains, optimizing space and improving operational efficiency.']

──────────────────────────────────────────────────

Question 15

🏷️ Domain: Trucking Vendor Portal

❓ What is the 'trucking vendor portal' and what functionalities are expected from it in the SD1 system?

💡 Answer

 bottlenecks, planning maintenance activities, and ensuring smooth operations within the yard."]

──────────────────────────────────────────────────

Question 35

🏷️ Domain: Bulk (Heap) Depot Location ❓ What is a 'bulk (heap) depot location' and how does it differ from stack-based logic in SD1?

💡 Answer

["A 'bulk (heap) depot location' refers to a storage area in warehouse or inventory management systems where materials are stored in large, loose quantities (bulk or heap), rather than in discrete, organized units. This is common for items like sand, gravel, or other materials that are not easily stacked or counted individually.", "In contrast, stack-based logic (as used in SD1, which refers to SAP's Warehouse Management module) involves storing materials in organized stacks or bins, where each item or pallet is placed in a specific, trackable location. Stack-based logic is suitable for items that can be counted and stacked, such as boxes or pallets.", 'The main difference is that bulk (heap) depot locations are for loose, unorganized storage, while stack-based logic is for organized, trackable storage of discrete items.']

──────────────────────────────────────────────────

y process maps [if exist] to be added here   

(Appendix) Functional Sign-offs and other artefacts, appended to the BRD (Mandatory to evaluate)

 
Appendix 1 – Survey result for operational challenges Appendix 2: List of capabilities and definitions

Appendix 3-Rationale for The Yard Management Design Assessment:
We are requesting this effort based on the following key observations and facts, which indicate potential risks that could impact the success and scalability of the solution

The SD1 YM codebase was originally developed by an external vendor, Baufest. After their departure from the project, newly onboarded external developers attempted to leverage the existing codebase to meet the requirements for the Chicago implementation. However, feedback indicates that the legacy codebase has posed significant challenges in terms of scalability and enhancement.

For example the depot layout overview  utilizes the Google Maps API. When I asked the rationale behind this, the response indicated that it was inherited from the original codebase and deemed difficult to modify.

ctural improvement.

Prioritize critical missing capabilities to be incorporated into the SD1 system before larger roll out

Align cross-platform initiatives and secure integration points, including:  Asset management solution (e-EIR)

Equipment maintenance solution (CMMS)

Global EHS solution

Scope of this BRD

In Scope:

Safety

Incorporating safety elements into yard design, planning and scheduling logic wherever possible and provide visibility for safety risk getting data from global EHS system or manual entry, to ensure compliance and foster a secure operational environment.

Efficiency in yard planning and execution

Improved Yard/container automatic suggestion quality and ability to measure/report value with automatic assignment (e.g. number of unproductive move or increased space utilization). Additional info: In MVP version automatic container gate out plan adopted only %18 of cases.

systems are involve...
Q32: What is the 'booking pre-confirmation' process and how is it currently managed v...
Q33: What is the 'appointment system' for truckers in SD1, and what are the challenge... systems are involve...
Q32: What is the 'booking pre-confirmation' process and how is it currently managed v...
Q33: What is the 'appointment system' for truckers in SD1, and what are the challenge... Q34: What is the 'depot layout overview' feature in SD1 and how does it support yard ...
Q35: What is a 'bulk (heap) depot location' and how does it differ from stack-based l...
Q36: What is a 'container restriction reason' and how is it managed in SD1?
Q37: What are 'full container operations' and what interim solutions were introduced ...
Q38: What is 'container address accuracy control' and how is it implemented in SD1?
Q39: What is the 'yard cockpit dashboard' and what key metrics and functionalities do...
Q40: What is the 'yard rule engine' and how does it support BCO customer-specific SLA...
Q41: What is the 'yard task scheduler' and how does it optimize task assignment and p...
Q42: What is the interface between DMS and customs systems, and what data is exchange...


Question 1

erefore a critical enabler for improving yard planning efficiency.

Overview of the customer experience

There are primarily 4 personas that will see the impact of YM implementation on a regular basis Reach Truck Operators: Use mobile apps for guided placement and task visualization.

Yard Planners: Digitally create layouts, optimize space, and manage yard activities.

Yard Supervisors: Monitor equipment, receive alerts, and ensure safety compliance.

EHSS: More visibility about site risk levels and incidents

The current SD1 design separates reports and actions into different menus, making it challenging to provide relevant personas with the required capabilities and increasing the learning curve. Additionally, users are unable to personalize views and filters to suit their specific needs or share with others. To address these challenges, we plan to introduce key worklists that will combine data display and actions, enhancing both guided user journey and efficiency [a well adopted functionality in landside TMS].

The mock-up above illustrates one of the worklists we aim to deliver:

Requirement statements (PPO driven)

Success criteria, Metrics, and KPIs (PPO driven)

'- MVP computers are joined to the WRB Domain, and the architecture is designed to be replicable for future regional expansion, with each region connecting to the WRB Azure Hub but otherwise remaining '- MVP computers are joined to the WRB Domain, and the architecture is designed to be replicable for future regional expansion, with each region connecting to the WRB Azure Hub but otherwise remaining independent.', 'Proposed changes for modernization and regional expansion include:', '- Using the same Active Directory Domain (WRB) for both LFL and future regional expansions to avoid the operational and security complexities of managing multiple domains.', '- Ensuring that each regional deployment remains largely independent, with only necessary connectivity to the WRB Azure Hub, to maintain security and operational boundaries.', '- Careful consideration of security concerns related to shared Active Directory environments, with an emphasis on monitoring, backup, and recovery, and minimizing cross-contamination risks.', '- The architecture is designed to support future regional expansion by replicating the MVP model, allowing each new region to be set up independently but with consistent identity and authentication via the WRB Azure Hub.', '- There is ongoing assessment of the required level of connectivity between regions and between the AWS LF Logistics Landing Zone and the AWS

charge).', '2. Coordination with multiple stakeholders (shippers, packinghouses, cold storage, landside execution teams, truckers, terminal reefer vendors, vessel operators, and claims managers).', '3 charge).', '2. Coordination with multiple stakeholders (shippers, packinghouses, cold storage, landside execution teams, truckers, terminal reefer vendors, vessel operators, and claims managers).', '3 Use of systems like GCSS for haulage updates and email notifications to ensure all parties are informed and records are updated.', '4. Application of administrative controls such as setpoint change fees and bill of lading clauses to protect the carrier and ensure transparency.', '5. Implementation of IoT-based Remote Container Management (RCM) systems, which use sensors and cloud connectivity to monitor and manage reefer container conditions in real time, enabling quick identification and rectification of issues.', "6. Lifecycle management of IoT devices, including onboarding, maintenance, firmware updates, and decommissioning, typically managed through a dedicated portal (e.g., Maersk's RCM portal).", '7. Security and compliance measures, including penetration testing and certificate management for device authentication and data integrity.']

s UI/UX."]

──────────────────────────────────────────────────

Question 31

🏷️ Domain: EDI Integrations

❓ What are the key EDI integrations required for SD1, and what systems are involved?

💡 Answer ['The key EDI integrations required for SD1 (Service Desk 1) involve several systems and components:', '1. Core EDI Integration Platforms: Axway, Azure API Management (APIM), and Opentext are listed as main integration platforms.', '2. Application Hosting and Infrastructure: The EDI/B2B Europe applications are hosted on Red Hat Enterprise Linux (RHEL 7.4) with Oracle Database (********.0) and Oracle Fusion Middleware/Weblogic (********). The infrastructure is deployed in Azure (West Europe region) with multiple servers for production (e.g., apdafusiob2b01, apdafusiob2b03, etc.).', '3. Upstream and Downstream Applications: EDI message flows are two-way, so applications connected to Integration B2B Europe are both upstream and downstream. The specific list of these applications can be found in the CMDB in ServiceNow (SNOW).', '4

nt and documentation.']

──────────────────────────────────────────────────

Question 19

🏷️ Domain: DMS

❓ What does 'DMS' stand for in this context and what is its role in yard management?

💡 Answer ["In the context of yard management, 'DMS' typically stands for 'Dock Management System.'", 'A Dock Management System (DMS) is a software solution or platform used to manage and optimize the operations at loading docks within a yard. Its role includes scheduling dock appointments, tracking the movement of trailers and trucks, coordinating loading and unloading activities, and improving overall efficiency and safety in the yard.']

──────────────────────────────────────────────────

Question 20

🏷️ Domain: RKEM

❓ What does 'RKEM' stand for and what is its function in the context of EDI integrations?

💡 Answer

ty of goods.', '6. Trained personnel familiar with bonded warehouse operations and customs compliance.']

──────────────────────────────────────────────────

Question 6

🏷️ Domain: Stuffing/Destuffing ❓ What do 'stuffing' and 'destuffing' refer to in depot operations, and why are they out of scope for this BRD?

💡 Answer

["In depot operations, 'stuffing' refers to the process of loading cargo into a container, while 'destuffing' refers to unloading cargo from a container. These terms are used to describe the physical handling of goods in and out of containers at depots or warehouses.", "Within the context of the provided document, 'stuffing' and 'unstuffing' (destuffing) are also used as status indicators in the Container Loading Result (CLR) process. For example, a CLR can be in 'draft', 'planned', 'submitted', or 'stuffed' status, with 'stuffed' indicating that the container has been loaded (stuffed) and is ready for further processing

ty in the yard.']

──────────────────────────────────────────────────

Question 20

🏷️ Domain: RKEM

❓ What does 'RKEM' stand for and what is its function in the context of EDI integrations?

💡 Answer ["'RKEM' stands for 'Equipment Management System' used by Maersk Line. Its primary function is to control and manage Maersk Line's equipment fleet and assets, with a focus on container control. RKEM is used globally across all Maersk offices and agencies to track equipment, generate inventory reports, and pull bay-plans.", 'In the context of EDI (Electronic Data Interchange) integrations, RKEM receives equipment movement data from various upstream systems (such as EDI/AMPS, GCSS, MERC+, Powerstow) via interfaces like RKIP and MQ (Message Queue). EDI messages are converted into RKIP data structures before being inserted into RKEM. RKEM also sends data to many downstream applications, supporting real-time equipment tracking and validation for other business processes.', "RKEM is a core system in Maersk's IT landscape, integrating with booking, documentation, vessel scheduling, and product catalog systems

he import delivery point. This avoids the need to return the empty container to the depot or port, thereby reducing unnecessary empty container moves.", "Triangulation is only possible for truck moves he import delivery point. This avoids the need to return the empty container to the depot or port, thereby reducing unnecessary empty container moves.", "Triangulation is only possible for truck moves If either the import or export plan is not set to 'Truck' as the transport mode, the system will automatically change it to 'Truck' to enable triangulation.", "There are two main types of triangulation: (1) Direct Triangulation, which is used when the container size and type match between the import and export bookings, and (2) Triangulation using 'Change on Depot', which allows for a depot change if the container types or sizes do not match exactly.", 'Triangulation optimizes container moves by reducing the number of empty repositioning trips, saving costs, time, and reducing environmental impact. It is a key method for improving efficiency in container logistics.']

──────────────────────────────────────────────────

Question 30

🏷️ Domain: UI/UX

ication between terminal departments and external parties (e.g., customs, shipping companies).', '- Analysis and reporting: Generates data and reports on operational efficiency, equipment performance, ication between terminal departments and external parties (e.g., customs, shipping companies).', '- Analysis and reporting: Generates data and reports on operational efficiency, equipment performance, and other key indicators for informed decision-making.', 'Business impacts of the dashboard include improved operational efficiency, cost reduction, enhanced safety and compliance, better customer satisfaction, increased competitiveness, and data-driven resource optimization.']

──────────────────────────────────────────────────

Question 40

🏷️ Domain: Yard Rule Engine

❓ What is the 'yard rule engine' and how does it support BCO customer-specific SLAs?

💡 Answer

er interfaces."]

──────────────────────────────────────────────────

Question 21

🏷️ Domain: CMMS

❓ What does 'CMMS' stand for and how does it integrate with SD1 for equipment maintenance?

💡 Answer ['CMMS stands for Computerized Maintenance Management System. It is a software system designed to help organizations manage and automate their maintenance operations, including scheduling, tracking, and documenting maintenance activities for equipment and facilities.', 'Integration with SD1 (which may refer to a specific software, system, or module related to equipment maintenance) typically involves sharing data such as equipment status, maintenance schedules, work orders, and performance metrics. This integration allows for seamless communication between the CMMS and SD1, improving efficiency, reducing downtime, and ensuring that maintenance tasks are performed on time. The exact nature of the integration depends on what SD1 specifically refers to in the context of the organization.']

──────────────────────────────────────────────────

Question 22

🏷️ Domain: MOS

❓ What does 'MOS' stand for and what is its relevance to system and process adoption in SD1?

💡 Answer

or service.']

──────────────────────────────────────────────────

Question 24

🏷️ Domain: Depot Archetypes

❓ What are 'depot archetypes' and how do they influence the rollout plan for SD1?

💡 Answer ["'Depot archetypes' refer to standardized categories or models of depots, typically defined by their size, function, operational complexity, or other key characteristics. These archetypes help organizations classify their depots into groups with similar requirements and challenges.", 'In the context of a rollout plan for SD1 (which could refer to a system deployment, software, or process), depot archetypes influence the plan by allowing the rollout to be tailored to the specific needs of each archetype. For example, a large, complex depot may require more resources, training, or a different implementation approach compared to a small, simple depot.', 'By using depot archetypes, the rollout plan can be more efficient and effective, as it can address the unique needs of each group rather than treating all depots the same. This can help in prioritizing which depots to roll out to first, allocating resources appropriately, and anticipating potential challenges.']

nvironments (e.g., EDIPROD, EDIPP, EDITST) across different servers and queue managers (e.g., scrbampdk001659, scrbampdk001653, etc.) is essential for inbound and outbound EDI message processing.', '6 nvironments (e.g., EDIPROD, EDIPP, EDITST) across different servers and queue managers (e.g., scrbampdk001659, scrbampdk001653, etc.) is essential for inbound and outbound EDI message processing.', '6 Monitoring and Error Handling: The METRO EDI-Service (EDIS) is used for monitoring and tracking EDI message flows, providing transparency and error resolution across sender, middleware, and receiver systems.', 'In summary, the key EDI integrations for SD1 require coordination between integration platforms (Axway, Azure APIM, Opentext), infrastructure (Linux, Oracle, Azure), message queue systems, and a variety of upstream/downstream business applications, all monitored and managed via EDIS and ServiceNow CMDB.']

──────────────────────────────────────────────────

Question 32

🏷️ Domain: Booking Pre-confirmation

❓ What is the 'booking pre-confirmation' process and how is it currently managed versus the proposed automated approach?

💡 Answer

──────────────

Question 5

🏷️ Domain: Bonded Operations

❓ What are 'bonded operations' in the context of depot management, and what specific capabilities are required for these operations?

💡 Answer ["In the context of depot management, 'bonded operations' refer to activities involving goods stored in a bonded warehouse or depot. These are secure areas where imported goods can be stored, manipulated, or undergo certain processes without the payment of customs duties until the goods are released for domestic use or export.", 'Specific capabilities required for bonded operations include:', '1. Compliance with customs regulations and procedures for bonded goods.', '2. Secure storage facilities that meet customs requirements.', '3. Inventory management systems to track bonded goods accurately.', '4. Processes for handling, repackaging, or processing goods under customs supervision.', '5. Documentation and reporting systems to ensure traceability and accountability of goods.', '6. Trained personnel familiar with bonded warehouse operations and customs compliance.']

──────────────────────────────────────────────────

Question 6

🏷️ Domain: Stuffing/Destuffing

ventory management and loss prevention.', '4. Communication and Coordination: Facilitates communication between internal departments and external stakeholders (e.g., customs, shipping companies).', '5 ventory management and loss prevention.', '4. Communication and Coordination: Facilitates communication between internal departments and external stakeholders (e.g., customs, shipping companies).', '5 Analysis and Reporting: Generates operational data and reports to support informed decision-making and continuous improvement.', 'These functionalities enable centralized and automated management of critical aspects of port operations, improving efficiency, safety, compliance, and customer satisfaction.']

──────────────────────────────────────────────────

Question 5

🏷️ Domain: Bonded Operations

❓ What are 'bonded operations' in the context of depot management, and what specific capabilities are required for these operations?

💡 Answer

 History Report | As a Yard Planner, I want to analyze historical yard activity trend and waiting times, customer SLA, so that I can identify bottlenecks, rush hours and improvement opportunities | P2 Yard Rule Engine | As a Yard Supervisor, I want to define BCO customer specific SLA for delivering containers after getting booking so that I can report my performance. | P2

Yard Task Scheduler | As a Yard Planner, I want a system has a task scheduler that will enable
• Automated assignment of tasks to operators based on availability, proximity to the task, and skill level.
• Dynamic prioritization of tasks based on urgency, customer SLA, such as high-priority container moves for gate-out or repositioning for storage optimization.
• System recommendations for optimal task sequences (including triangulation gate in combined with gate out or move from same location) to minimize equipment travel time and reduce bottlenecks.
So that we improve productivity and reduce customer pain points. | P2

Risk | Mitigation Plan | Owner

System design limitation for enhancements due to reasons listed in Appendix 2 | Perform a deep design assessment and enhancement easiness | Engineering Manager

ts can be quickly adapted to, supporting tailored service levels. The platform is monitored for availability and performance, with dashboards and alerting in place to ensure SLA commitments are met.'] ──────────────────────────────────────────────────

Question 41

🏷️ Domain: Task Scheduler

❓ What is the 'yard task scheduler' and how does it optimize task assignment and prioritization?

💡 Answer

["There is no widely recognized or standard tool specifically called the 'yard task scheduler' in mainstream task scheduling literature or software documentation as of June 2024.", "If 'yard task scheduler' refers to a specific proprietary or niche system, there is no information provided in the documents or available sources to describe its mechanisms or optimization strategies.", 'Generally, a task scheduler is a system that assigns and prioritizes tasks based on certain criteria (such as deadlines, resource availability, or task importance) to optimize efficiency and resource utilization.', "Without more context or documentation, it is not possible to provide details on how the 'yard task scheduler' specifically optimizes task assignment and prioritization."]

provided content."]

──────────────────────────────────────────────────

Question 23

🏷️ Domain: SUS

❓ What does 'SUS' stand for in the context of measuring efficiency or user satisfaction?

💡 Answer ["In the context of measuring efficiency or user satisfaction, 'SUS' stands for 'System Usability Scale.'", 'The System Usability Scale is a widely used questionnaire that provides a quick and reliable way to evaluate the usability of a system, product, or service.']

──────────────────────────────────────────────────

Question 24

🏷️ Domain: Depot Archetypes

❓ What are 'depot archetypes' and how do they influence the rollout plan for SD1?

💡 Answer

age flows are two-way, so applications connected to Integration B2B Europe are both upstream and downstream. The specific list of these applications can be found in the CMDB in ServiceNow (SNOW).', '4 age flows are two-way, so applications connected to Integration B2B Europe are both upstream and downstream. The specific list of these applications can be found in the CMDB in ServiceNow (SNOW).', '4 EDI Message Flows: EDI messages (such as EDIFACT, ANSI-X12) are exchanged between business partners and internal systems, supporting business transactions like shipping instructions, booking requests, job orders, and customs manifests.', '5. Message Queues: Integration with various MQ (Message Queue) environments (e.g., EDIPROD, EDIPP, EDITST) across different servers and queue managers (e.g., scrbampdk001659, scrbampdk001653, etc.) is essential for inbound and outbound EDI message processing.', '6

organization.']

──────────────────────────────────────────────────

Question 22

🏷️ Domain: MOS

❓ What does 'MOS' stand for and what is its relevance to system and process adoption in SD1?

💡 Answer ["'MOS' in the context of the provided document does not appear as an acronym or term. Instead, the document refers to 'MODS', which stands for 'Maersk Logistics Operational Documentation System'. MODS is relevant to system and process adoption in SD1 (Service Desk 1) because it is a key application for handling physical cargo and shipping documentation. The Service Desk (SD) is responsible for supporting users in software installation, keyboard configuration, and assisting with session files in MODS. SD1 is also expected to educate users about changes in MODS processes, such as password reset procedures, to streamline support and reduce effort. Therefore, MODS is central to system and process adoption in SD1, but there is no mention of 'MOS' in the provided content."]

──────────────────────────────────────────────────

Question 23

🏷️ Domain: SUS

❓ What does 'SUS' stand for in the context of measuring efficiency or user satisfaction?

💡 Answer

─────────────────────────────────

Question 27

🏷️ Domain: Google Maps API

❓ How is the Google Maps API utilized in the SD1 YM codebase, and what are the challenges associated with its use?

💡 Answer ['There are no specific documents provided about the SD1 YM codebase or its use of the Google Maps API. Therefore, a direct answer based on the codebase is not possible from the available information.', 'Generally, the Google Maps API is commonly used in codebases to display maps, geolocate users, plot markers, calculate routes, and provide location-based services. Challenges often include handling API key security, managing usage quotas and costs, dealing with API changes or deprecations, ensuring performance and responsiveness, and handling errors or rate limits.']

──────────────────────────────────────────────────

Question 28

🏷️ Domain: Baufest

❓ Who is Baufest and what was their role in the development of the SD1 YM codebase?

💡 Answer

the depot layout overview  utilizes the Google Maps API. When I asked the rationale behind this, the response indicated that it was inherited from the original codebase and deemed difficult to modify. Furthermore, due to evolving scope requirements during the MVP phase, several workarounds were introduced, such as those related to full container operations. These interim solutions, while addressing immediate needs, might have caused suboptimal design, impacting long-term system enhancements.

While testing the YM capabilities I observed several consistency and design issues. for example, 

you can assign a full container restriction reason on an empty container 

you can assign a sales container to normal booking or a normal container to sales booking. 

you can perform truck departure without performing the container unloading operation. 

you can set container restrictions via excel upload, but it does not allow adding restriction reason which you need to edit one by one after the upload.

You can create a bulk(heap) depot location but as it was derivate from stack based logic system still generates dummy row and bay ids. See below 

proved for repair
and from the same worklist I could take following actions : 
e) create container move request among locations
f) trigger an ad hoc yard inspection and add damage and observation | P0 Yard Container Work List | As a Yard Planner, I want to search container for detailed criterias and able to take bulk actions like  create move order, restrict, preassign to booking, change container key details in the yard , so that they can ensure stock accuracy and prevent mistakes. | P0

Yard Layout Design | As a Yard Planner, I want to create and update bulk areas without row/bay details, so that I can facilitate data entry. | P0

Yard Work Order Worklist | As a Yard Planner, I want to plan, assign and prioritize yard move / check work orders , so that we optimize capacity and keep the yard in order | P0

Booking Management | As a Yard Planner, I want to have an automatic flow of full container bookings from shipping line or BCOs so that I can plan effectively the gate moves | P1

Booking Management | As a Yard Planner, I want to be able to reactivate cancelled bookings in specific cases with ability of attaching relevant audit proof so that I avoid manual booking creation | P1

cating that the BRD is concerned with digital or system-side processes, not the physical depot operations."]

──────────────────────────────────────────────────

Question 7

🏷️ Domain: OP Plan/OP Call ❓ What does 'OP plan' and 'OP call' refer to in this context?

💡 Answer

["'OP plan' typically stands for 'Operating Plan' or 'Operational Plan.' It refers to a detailed plan outlining the actions, resources, and timelines required to achieve specific business objectives within an organization. The OP plan usually covers a set period (such as a fiscal year) and includes financial targets, key initiatives, and performance metrics.", "'OP call' usually refers to a meeting or conference call where the Operating Plan is discussed, reviewed, or presented. During an OP call, leadership and relevant stakeholders may go over the progress, challenges, and next steps related to the operational plan. These calls are often scheduled at regular intervals (e.g., quarterly or annually) to ensure alignment and accountability."]

──────────────────────────────────────────────────

Question 8

🏷️ Domain: OHP

❓ What is 'OHP' and how does it relate to landside experience in Maersk's operations?

💡 Answer

ssign restriction reasons to containers, monitor restricted containers, and manage the workflow for lifting or changing restrictions."]

──────────────────────────────────────────────────

Question 37 🏷️ Domain: Full Container Operations

❓ What are 'full container operations' and what interim solutions were introduced in the SD1 MVP?

💡 Answer

["'Full container operations' refers to the handling, movement, and management of shipping containers that are fully loaded, typically in the context of ports, terminals, or logistics operations. This includes processes such as receiving, storing, moving, and dispatching containers that are not empty.", 'Regarding the SD1 MVP (Minimum Viable Product), there is no specific information provided in the documents about what interim solutions were introduced for full container operations. Therefore, based on the available information, we cannot detail the interim solutions for full container operations in the SD1 MVP.']

──────────────────────────────────────────────────

Question 38

🏷️ Domain: Container Address Accuracy Control

❓ What is 'container address accuracy control' and how is it implemented in SD1?

💡 Answer

is implemented by verifying and validating address data at various stages of the container's lifecycle, using both manual and automated processes."]

────────────────────────────────────────────────── Question 39

🏷️ Domain: Yard Cockpit Dashboard

❓ What is the 'yard cockpit dashboard' and what key metrics and functionalities does it provide?

💡 Answer

through a dedicated portal (e.g., Maersk's RCM portal).", '7. Security and compliance measures, including penetration testing and certificate management for device authentication and data integrity.'] ──────────────────────────────────────────────────

Question 17

🏷️ Domain: Chassis Operations

❓ What are 'chassis operations' and how are they managed within the yard management system?

💡 Answer

['Chassis operations refer to the management and movement of chassis, which are the wheeled frames used to transport shipping containers, within a logistics or intermodal yard.', 'Within a yard management system (YMS), chassis operations are managed by tracking the location, status, and allocation of chassis. The YMS helps coordinate which chassis are available, which are in use, and where they are located in the yard. It may also handle maintenance schedules, assignment to specific containers or trucks, and optimize the flow of chassis to reduce bottlenecks and improve efficiency.']

──────────────────────────────────────────────────

Question 18

🏷️ Domain: AEMS

❓ What does 'AEMS' stand for and how does it relate to e-EIR and DMS integration?

💡 Answer

─────────────────────────────────────

Question 15

🏷️ Domain: Trucking Vendor Portal

❓ What is the 'trucking vendor portal' and what functionalities are expected from it in the SD1 system?

💡 Answer ["The 'trucking vendor portal' in the SD1 system refers to Maersk's 'Supplier Connect' (previously called Supplier Portal), a web-based platform designed for external transport vendors such as trucking companies. Its main functionalities include: viewing transport (haulage) orders, planning transports, and sending updates to Maersk. Vendors can self-register, manage their orders, and communicate with Maersk through the portal. The system is built using Java (Spring Boot), deployed on Azure, and is accessible via a Maersk subdomain.", 'Additionally, related systems like Draywatch provide further functionalities for trucking companies, such as receiving electronic work orders, accepting/declining orders, updating order statuses (e.g., equipment departed/arrived), submitting invoices (including accessorial and miscellaneous charges), and tracking invoice/payment status. These systems facilitate collaboration between Maersk and trucking vendors throughout the inland transport lifecycle.']

n which you need to edit one by one after the upload.

You can create a bulk(heap) depot location but as it was derivate from stack based logic system still generates dummy row and bay ids. See below   The core capability of YM MVP version is empty container automatic planning (location assignment for inbound and container assignment four outbound). However, the functionality is not used by Chicago team. Each time operator manually overrides system proposal. The yard leader thinks the proposal is not effective. We do not have a fact-based proof that SD1 logic is better or worse. We have not reinforced the adoption neither from change management nor MOS perspective.

Basic optimization elements are missing for example two trucks are waiting in yard one is for container unloading and another for loading identical containers. System does not propose bundling those and eliminating unnecessary yard move [kind of triangulation]

UI/UX : Lack of guiding user logic and dashboard for depot leaders. Additionally for some yard activities global functions like EHS, equipment maintenance are proposing their vertical solutions which may cause bad user experience of dealing with many systems

ic SLA...
Q41: What is the 'yard task scheduler' and how does it optimize task assignment and p...
Q42: What is the interface between DMS and customs systems, and what data is exchange...


Question 1 🏷️ Domain: SD1

❓ What does SD1 stand for in the context of Maersk's operations?

💡 Answer

["In the context of Maersk's operations, 'SD1' most commonly refers to 'Service Delivery 1', which is an internal designation for a specific shipping service or route within Maersk's global network. Maersk uses such codes (like SD1, AE1, etc.) to identify and manage their various shipping lanes and operational services. However, without specific documents provided, this is the most likely interpretation based on industry usage and Maersk's naming conventions."]

──────────────────────────────────────────────────

Question 2

🏷️ Domain: SD1

❓ What are the main differences between the limited version of SD1 deployed in Latin America and the MVP version implemented in Chicago?

💡 Answer

ization.']

──────────────────────────────────────────────────

Question 40

🏷️ Domain: Yard Rule Engine

❓ What is the 'yard rule engine' and how does it support BCO customer-specific SLAs?

💡 Answer ["The 'yard rule engine' refers to a business rules management system that externalizes business rules from core business processes, allowing for dynamic rule creation and management by business users. In the Maersk context, this is implemented as the 'Decision Hub', an in-house platform built on the open-source Kogito stack. It features a business-centric UI that enables both non-technical and technical users to design, manage, and deploy rules via rule tables or DMN (Decision Model and Notation).", 'This engine supports BCO (Beneficial Cargo Owner) customer-specific SLAs by allowing business users to define and update rules that govern how SLAs are enforced and monitored, without requiring code changes. This flexibility ensures that customer-specific requirements can be quickly adapted to, supporting tailored service levels. The platform is monitored for availability and performance, with dashboards and alerting in place to ensure SLA commitments are met.']

Question 36

🏷️ Domain: Container Restriction Reason

❓ What is a 'container restriction reason' and how is it managed in SD1?

💡 Answer ["A 'container restriction reason' typically refers to a code or explanation used in logistics, warehousing, or supply chain management systems to indicate why a particular container is restricted from certain actions (such as movement, loading, or unloading).", "In the context of SD1 (which may refer to a specific software system or module, though the exact system is not specified in the query), managing a 'container restriction reason' would involve recording, tracking, and possibly resolving the reasons why containers are restricted. This could include setting restriction codes, updating statuses, and allowing authorized users to view or modify these reasons as needed.", "Without specific documentation or details about SD1, the general process involves using the system's interface to assign restriction reasons to containers, monitor restricted containers, and manage the workflow for lifting or changing restrictions."]

──────────────────────────────────────────────────

Question 37

) process. For example, a CLR can be in 'draft', 'planned', 'submitted', or 'stuffed' status, with 'stuffed' indicating that the container has been loaded (stuffed) and is ready for further processing ) process. For example, a CLR can be in 'draft', 'planned', 'submitted', or 'stuffed' status, with 'stuffed' indicating that the container has been loaded (stuffed) and is ready for further processing If a CLR is 'unstuffed', it means the container has been unloaded and the status reverts accordingly.", "The reason 'stuffing' and 'destuffing' are out of scope for the referenced Business Requirements Document (BRD) is likely because the BRD focuses on data updates, reporting, and system processes (such as updating remarks, custom fields, and batch uploads) rather than the physical operations of loading and unloading containers. The document specifically mentions that certain data updates can be made without requiring the container to be unstuffed and restuffed, indicating that the BRD is concerned with digital or system-side processes, not the physical depot operations."]

──────────────────────────────────────────────────

Question 7

🏷️ Domain: OP Plan/OP Call