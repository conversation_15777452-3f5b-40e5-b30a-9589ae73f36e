import sys
import os

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel, <PERSON>
from typing import List, Optional, Dict, Any
import asyncio
import json
import logging
import uvicorn

from dotenv import load_dotenv
from qdrant_http_client import QdrantHTTPClient

load_dotenv()

try:
    from langchain_openai import AzureChatOpenAI, AzureOpenAIEmbeddings
    from langchain_core.prompts import ChatPromptTemplate
    from langchain_core.messages import AIMessage, HumanMessage, SystemMessage

    model = AzureChatOpenAI(
        azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT_4_1"),
        api_key=os.getenv("AZURE_OPENAI_API_KEY_4_1"),
        api_version=os.getenv("AZURE_OPENAI_VERSION_4_1"),
        deployment_name="gpt-4o", 
        temperature=0.1,
    )

    embedding_llm = AzureOpenAIEmbeddings(
        azure_endpoint=os.getenv("EMBEDDING_OPENAI_API_BASE"),
        api_key=os.getenv("EMBEDDING_OPENAI_API_KEY"),
        api_version=os.getenv("EMBEDDING_OPENAI_API_VERSION"),
        deployment="text-embedding-3-large"
    )

except ImportError as e:
    print(f"Warning: Could not import LangChain modules: {e}")
    model = None
    embedding_llm = None

logger = logging.getLogger(__name__)

app = FastAPI(title="BA Agent API", description="Business Analyst Agent for generating user stories from BRD/PRD")

class GenerateStoriesRequest(BaseModel):
    document_name: str = Field(description="Name of the document in vector store")
    query: Optional[str] = Field(default=None, description="Optional specific query for retrieval")
    collection_name: Optional[str] = Field(default=None, description="Collection name (uses env var if not provided)")

class JiraIssue(BaseModel):
    title: str = Field(description="The title of the Jira issue.")
    description: str = Field(description="Detailed description of the Jira issue.")
    acceptance_criteria: str = Field(description="Acceptance criteria for the Jira issue.")
    priority: str = Field(description="Priority level of the Jira issue (e.g., High, Medium, Low).")
    story_points: int = Field(description="Story points assigned to the Jira issue for estimation.")

class JiraEpic(BaseModel):
    title: str = Field(description="The title of the Jira epic.")
    description: str = Field(description="Detailed description of the Jira epic.")
    issues: List[JiraIssue] = Field(description="List of issues associated with this epic.")

class GenerateStoriesResponse(BaseModel):
    success: bool
    epic: Optional[JiraEpic] = None
    error: Optional[str] = None
    context_length: Optional[int] = None

class QdrantRetriever:
    def __init__(self,
                 client: QdrantHTTPClient,
                 collection_name: str):
        """
        Initialize Qdrant retriever using HTTP client

        Args:
            client: QdrantHTTPClient instance
            collection_name: Name of the collection in Qdrant
        """
        self.client = client
        self.collection_name = collection_name

    def search_relevant_chunks(
        self,
        query: str,
        limit: int = 10,
        score_threshold: float = 0.7,
        document_filter: Optional[str] = None
    ) -> List[dict]:
        try:
            print(f"Generating embedding for query: {query}")
            if not embedding_llm:
                raise ValueError("Embedding model not available")

            query_vector = embedding_llm.embed_query(query)

            print(f"Searching in collection '{self.collection_name}' for query: {query}")

            search_results = self.client.search_points(
                collection_name=self.collection_name,
                query_vector=query_vector,
                limit=limit,
                score_threshold=score_threshold
            )

            formatted_results = []
            if 'result' in search_results:
                points = search_results['result']
            else:
                points = search_results.get('points', [])

            for point in points:
                try:
                    payload = point.get("payload", {})

                    if document_filter and payload.get('document_name') != document_filter:
                        continue

                    if "_node_content" in payload:
                        node = json.loads(payload["_node_content"])
                        content = node.get("text", "")
                        metadata = node.get("metadata", {})
                    else:
                        content = payload.get("content", "")
                        metadata = payload.get("metadata", {})

                except (json.JSONDecodeError, KeyError):
                    content = payload.get("content", "")
                    metadata = payload.get("metadata", {})

                formatted_results.append({
                    'content': content,
                    'metadata': metadata,
                    'score': point.get('score', 0),
                    'document_name': payload.get('document_name', ''),
                    'chunk_id': payload.get('chunk_id', str(point.get('id', '')))
                })

            print(f"Found {len(formatted_results)} relevant chunks")
            return formatted_results

        except Exception as e:
            logger.error(f"Error in search: {e}")
            return []

    def get_all_document_content(self, document_name: Optional[str] = None) -> str:
        """Get all content from a document using scroll (no vector search needed)"""
        try:
            print(f"Retrieving all content for document: {document_name or 'all documents'}")

            # Use scroll to get all points
            scroll_results = self.client.scroll_points(
                collection_name=self.collection_name,
                limit=1000,
                with_payload=True
            )

            chunks = []
            if 'result' in scroll_results:
                points = scroll_results['result'].get('points', [])
                for point in points:
                    payload = point.get('payload', {})

                    # Filter by document if specified
                    if document_name and payload.get('document_name') != document_name:
                        continue

                    try:
                        if "_node_content" in payload:
                            node = json.loads(payload["_node_content"])
                            content = node.get("text", "")
                        else:
                            content = payload.get("content", "")
                    except (json.JSONDecodeError, KeyError):
                        content = payload.get("content", "")

                    if content.strip():
                        chunks.append({
                            'content': content,
                            'document_name': payload.get('document_name', ''),
                            'chunk_id': payload.get('chunk_id', str(point.get('id', '')))
                        })

            print(f"Found {len(chunks)} content chunks")
            combined_content = [chunk['content'] for chunk in chunks]
            return "\n\n".join(combined_content)

        except Exception as e:
            logger.error(f"Error getting all content: {e}")
            return ""

    def get_context_by_query(
        self,
        query: str,
        document_name: Optional[str] = None,
        limit: int = 20,
        score_threshold: float = 0.5
    ) -> str:
        """Get context based on a specific user query"""
        print(f"Retrieving context for query: '{query}'")

        chunks = self.search_relevant_chunks(
            query=query,
            document_filter=document_name,
            limit=limit,
            score_threshold=score_threshold
        )

        if not chunks:
            print("No chunks found for query, falling back to all document content")
            return self.get_all_document_content(document_name)

        # Sort by relevance score
        chunks.sort(key=lambda x: x.get('score', 0), reverse=True)
        combined_content = [chunk['content'] for chunk in chunks]
        return "\n\n".join(combined_content)

    def get_available_documents(self) -> List[str]:
        """Get list of all available documents in the collection"""
        try:
            scroll_results = self.client.scroll_points(
                collection_name=self.collection_name,
                limit=1000,
                with_payload=True
            )

            documents = set()
            if 'result' in scroll_results:
                points = scroll_results['result'].get('points', [])
                for point in points:
                    payload = point.get('payload', {})
                    doc_name = payload.get('document_name', '')
                    if doc_name:
                        documents.add(doc_name)

            return sorted(list(documents))
        except Exception as e:
            logger.error(f"Error getting documents: {e}")
            return []

def initialize_qdrant_http_client(url: str = None, api_key: str = None) -> QdrantHTTPClient:
    """Initialize Qdrant HTTP client"""
    qdrant_url = url or os.getenv("QDRANT_ENDPOINT")
    qdrant_api_key = api_key or os.getenv("QDRANT_API_KEY")

    if not qdrant_url:
        raise ValueError("QDRANT_ENDPOINT environment variable is not set")
    if not qdrant_api_key:
        raise ValueError("QDRANT_API_KEY environment variable is not set")

    print(f"Connecting to Qdrant at: {qdrant_url}")
    return QdrantHTTPClient(
        url=qdrant_url,
        api_key=qdrant_api_key,
        timeout=60
    )

def test_qdrant_connection(client: QdrantHTTPClient, collection_name: str) -> bool:
    """Test Qdrant connection and collection availability"""
    try:
        collections = client.get_collections()
        collection_names = [c['name'] for c in collections.get('result', {}).get('collections', [])]
        print(f"Available collections: {collection_names}")

        if collection_name not in collection_names:
            raise ValueError(f"Collection '{collection_name}' not found in Qdrant")

        collection_info = client.get_collection_info(collection_name)
        points_count = collection_info.get('result', {}).get('points_count', 0)
        print(f"Collection '{collection_name}' found with {points_count} points")
        return True

    except Exception as e:
        print(f"Qdrant connection test failed: {e}")
        return False

BA_PROMPT = """
You are a Business Analyst tasked with generating Agile Scrum User Stories from Business Requirement Document (BRD) or Product Requirement Document (PRD) content.

The BRD/PRD content below contains business requirements retrieved from a vector database based on relevance.
Generate a JIRA epic and a list of Agile Scrum User Stories in that Epic following the structure below and return it as a JSON.

Important guidelines:
- Don't return AcceptanceCriteria as a JSON Array - it should be represented as a single field even if it is long multiline text.
- Ensure the JSON is not malformed in the response
- Use the provided context to understand the full scope of requirements

Structure for each story:
1. Title
2. Description (As a..., I want..., so that...)
3. AcceptanceCriteria (Given / When / Then)
4. Priority (High/Medium/Low)
5. StoryPoints (Fibonacci scale: 1, 2, 3, 5, 8, 13)

Guidelines:
- Follow the INVEST principle (Independent, Negotiable, Valuable, Estimable, Small, Testable)
- Avoid technical or implementation specifics
- Extract only actionable, testable features and behaviors
- Consider the context is for a Scrum team working in 2-week sprints
- The product feature set is intended to enhance user experience for Container Depots

Please analyze the context and generate complete, well-structured stories.
"""

if model:
    structured_llm = model.with_structured_output(JiraEpic)
    prompt_template = ChatPromptTemplate.from_messages([
        ("system", BA_PROMPT),
        ("human", "BRD/PRD Context from Vector Store:\n\n{content}"),
    ])
    BA_agent = prompt_template | structured_llm
else:
    structured_llm = None
    prompt_template = None
    BA_agent = None

def create_tasks_from_vector_store(state: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create tasks using context retrieved from Qdrant vector store using HTTP client

    Args:
        state: Dictionary containing:
            - document_name: Name of the document in vector store
            - query: Optional user query for specific context retrieval
            - qdrant_client: QdrantHTTPClient instance
            - collection_name: Name of the collection
    """
    print("------------------------BA Agent Vector Store Search-------------------------")

    document_name = state.get("document_name")
    user_query = state.get("query")
    qdrant_client = state.get("qdrant_client")
    collection_name = state.get("collection_name")

    if not document_name:
        raise ValueError("No document_name provided in state.")

    if not qdrant_client:
        raise ValueError("No qdrant_client provided in state.")

    if not collection_name:
        raise ValueError("No collection_name provided in state.")

    if not BA_agent:
        raise ValueError("BA agent not initialized - check model configuration")

    retriever = QdrantRetriever(
        client=qdrant_client,
        collection_name=collection_name
    )

    if user_query:
        print(f"Using user query: '{user_query}'")
        context = retriever.get_context_by_query(
            query=user_query,
            document_name=document_name,
            limit=20,
            score_threshold=0.5
        )
    else:
        print("No specific query provided, retrieving all document content for user story generation")
        context = retriever.get_all_document_content(document_name=document_name)

    if not context:
        raise ValueError(f"No relevant context found for document: {document_name}")

    logger.info(f"Retrieved context length: {len(context)} characters")

    # Generate user stories from the context
    epic = BA_agent.invoke({"content": context})

    return {
        "epic": epic,
        "context_length": len(context),
        "query_used": user_query,
        "retrieval_method": "query-based" if user_query else "full-document"
    }

@app.get("/")
async def root():
    return {"message": "BA Agent API is running", "status": "healthy"}

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "model_available": model is not None,
        "embedding_model_available": embedding_llm is not None
    }

@app.post("/generate-stories", response_model=GenerateStoriesResponse)
async def generate_stories_endpoint(request: GenerateStoriesRequest):
    """Generate user stories from BRD/PRD content in vector store"""
    try:
        qdrant_client = initialize_qdrant_http_client()
        collection_name = request.collection_name or os.getenv('QDRANT_BRD_COLLECTION_NAME')

        if not collection_name:
            raise HTTPException(status_code=400, detail="Collection name not provided and QDRANT_BRD_COLLECTION_NAME not set")

        if not test_qdrant_connection(qdrant_client, collection_name):
            raise HTTPException(status_code=503, detail="Cannot connect to Qdrant database")

        state = {
            "document_name": request.document_name,
            "query": request.query,
            "qdrant_client": qdrant_client,
            "collection_name": collection_name
        }

        result = create_tasks_from_vector_store(state)

        return GenerateStoriesResponse(
            success=True,
            epic=result["epic"],
            context_length=result["context_length"]
        )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error generating stories: {e}")
        return GenerateStoriesResponse(
            success=False,
            error=str(e)
        )

@app.get("/collections")
async def list_collections():
    """List available collections in Qdrant"""
    try:
        qdrant_client = initialize_qdrant_http_client()
        collections = qdrant_client.get_collections()
        return collections
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Cannot connect to Qdrant: {e}")

@app.get("/documents")
async def list_documents():
    """List available documents in the vector store"""
    try:
        qdrant_client = initialize_qdrant_http_client()
        collection_name = os.getenv('QDRANT_BRD_COLLECTION_NAME')

        if not collection_name:
            raise HTTPException(status_code=400, detail="QDRANT_BRD_COLLECTION_NAME not set")

        if not test_qdrant_connection(qdrant_client, collection_name):
            raise HTTPException(status_code=503, detail="Cannot connect to Qdrant database")

        retriever = QdrantRetriever(qdrant_client, collection_name)
        documents = retriever.get_available_documents()

        return {
            "documents": documents,
            "count": len(documents),
            "collection": collection_name
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

def main():
    """Main function for testing"""
    print("Starting BA Agent...")
    print("Initializing Qdrant HTTP client")
    print(f"Qdrant endpoint: {os.getenv('QDRANT_ENDPOINT')}")

    try:
        qdrant_client = initialize_qdrant_http_client()
        collection_name = os.getenv('QDRANT_BRD_COLLECTION_NAME')

        if not collection_name:
            raise ValueError("QDRANT_BRD_COLLECTION_NAME environment variable is not set")

        if not test_qdrant_connection(qdrant_client, collection_name):
            print("Cannot connect to Qdrant")
            return

        state = {
            "document_name": "BRD-SD1.docx",
            "qdrant_client": qdrant_client,
            "collection_name": collection_name
        }

        result = create_tasks_from_vector_store(state)
        epic = result["epic"]

        md_txt = f"""
Epic: {epic.title}
Description: {epic.description}
"""
        print(md_txt)

        for n, issue in enumerate(epic.issues, start=1):
            issue_txt = f"""
Story {n}: {issue.title}
Description: {issue.description}
Acceptance Criteria: {issue.acceptance_criteria}
Priority: {issue.priority}
Story Points: {issue.story_points}
"""
            print(issue_txt)
            print("-" * 40)
            md_txt += f"""------------------------------\n{issue_txt}"""

        with open("stories_from_vector_store.md", "w") as f:
            f.write(md_txt)

        print("Stories generated successfully from vector store!")

    except ValueError as e:
        print(f"Configuration Error: {e}")
        print("Please check your environment variables and try again.")
    except Exception as e:
        print(f" Error generating stories: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "server":
        uvicorn.run(app, host="0.0.0.0", port=8000)
    else:
        
        main()