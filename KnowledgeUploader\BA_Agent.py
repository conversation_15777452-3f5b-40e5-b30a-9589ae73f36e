import sys
import os
import tempfile
import uuid
from pathlib import Path

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from fastapi import FastAPI, HTTPException, UploadFile, File
from fastapi.responses import JSONResponse
from pydantic import BaseModel, <PERSON>
from typing import List, Optional, Dict, Any
import json
import logging
import uvicorn
from docx import Document

from dotenv import load_dotenv
from qdrant_http_client import QdrantHTTPClient

load_dotenv()

try:
    from langchain_openai import AzureChatOpenAI, AzureOpenAIEmbeddings
    from langchain_core.prompts import ChatPromptTemplate
    from langchain_core.messages import AIMessage, HumanMessage, SystemMessage

    model = AzureChatOpenAI(
        azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT_4_1"),
        api_key=os.getenv("AZURE_OPENAI_API_KEY_4_1"),
        api_version=os.getenv("AZURE_OPENAI_VERSION_4_1"),
        deployment_name="gpt-4o",
        temperature=0.1,
        max_tokens=4000
    )

    embedding_llm = AzureOpenAIEmbeddings(
        azure_endpoint=os.getenv("EMBEDDING_OPENAI_API_BASE"),
        api_key=os.getenv("EMBEDDING_OPENAI_API_KEY"),
        api_version=os.getenv("EMBEDDING_OPENAI_API_VERSION"),
        deployment="text-embedding-3-large"
    )

except ImportError as e:
    print(f"Warning: Could not import LangChain modules: {e}")
    model = None
    embedding_llm = None

logger = logging.getLogger(__name__)

app = FastAPI(
    title="Enhanced BA Agent API",
    description="Business Analyst Agent for processing BRDs and generating user stories with FAQ integration",
    version="2.0.0"
)

# Request/Response Models
class GeneratedQuestion(BaseModel):
    question: str
    domain: Optional[str] = None

class FAQAnswer(BaseModel):
    question: str
    answer: str
    score: float

class JiraIssue(BaseModel):
    title: str = Field(description="The title of the Jira issue.")
    description: str = Field(description="Detailed description of the Jira issue.")
    acceptance_criteria: str = Field(description="Acceptance criteria for the Jira issue.")
    priority: str = Field(description="Priority level of the Jira issue (e.g., High, Medium, Low).")
    story_points: int = Field(description="Story points assigned to the Jira issue for estimation.")

class JiraEpic(BaseModel):
    title: str = Field(description="The title of the Jira epic.")
    description: str = Field(description="Detailed description of the Jira epic.")
    issues: List[JiraIssue] = Field(description="List of issues associated with this epic.")

class BRDProcessResponse(BaseModel):
    success: bool
    filename: str
    text_length: int
    questions_generated: int
    faq_answers_found: int
    epic: Optional[JiraEpic] = None
    processing_steps: List[str]
    error: Optional[str] = None

# Global storage for processed documents (in production, use a database)
processed_documents = {}

class DocumentProcessor:
    """Handles DOCX file processing and text extraction"""

    @staticmethod
    def extract_text_from_docx(file_path: str) -> str:
        """Extract text content from DOCX file"""
        try:
            doc = Document(file_path)
            text_content = []

            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text.strip())

            # Also extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        if cell.text.strip():
                            text_content.append(cell.text.strip())

            return "\n\n".join(text_content)
        except Exception as e:
            logger.error(f"Error extracting text from DOCX: {e}")
            raise

class QuestionGenerator:
    """Generates questions from document content using LLM"""

    def __init__(self):
        if not model:
            raise ValueError("LLM model not available")
        self.model = model

    def generate_questions(self, document_text: str) -> List[GeneratedQuestion]:
        """Generate relevant questions from document text"""

        question_prompt = """
You are an expert Business Analyst. Analyze the following Business Requirements Document (BRD) and generate 10-15 specific, detailed questions that would help understand the requirements better.

Focus on generating questions about:
- Functional requirements and features
- User interface and user experience
- System integrations and technical requirements
- Business processes and workflows
- Data management and reporting
- Security and compliance requirements
- Performance and scalability needs

For each question, also suggest a domain/category (e.g., "UI/UX", "Integration", "Security", "Reporting", etc.)

Return the response as a JSON array with this format:
[
  {
    "question": "What are the specific user authentication requirements?",
    "domain": "Security",
    "priority": "High"
  }
]

Document Content:
{document_text}
"""

        try:
            response = self.model.invoke(question_prompt.format(document_text=document_text[:8000]))  # Limit text length

            # Parse JSON response
            questions_data = json.loads(response.content)

            questions = []
            for q_data in questions_data:
                questions.append(GeneratedQuestion(
                    question=q_data.get("question", ""),
                    domain=q_data.get("domain"),
                    priority=q_data.get("priority", "Medium")
                ))

            return questions

        except Exception as e:
            logger.error(f"Error generating questions: {e}")
            # Fallback: return some default questions
            return [
                GeneratedQuestion(question="What are the main functional requirements?", domain="Functional", priority="High"),
                GeneratedQuestion(question="What are the user interface requirements?", domain="UI/UX", priority="Medium"),
                GeneratedQuestion(question="What integration requirements are needed?", domain="Integration", priority="High")
            ]

class FAQSearcher:
    """Searches FAQ database for answers to generated questions"""

    def __init__(self, qdrant_client: QdrantHTTPClient, collection_name: str):
        self.client = qdrant_client
        self.collection_name = collection_name
        if not embedding_llm:
            raise ValueError("Embedding model not available")
        self.embedding_model = embedding_llm

    def search_faq_answers(self, questions: List[GeneratedQuestion]) -> List[FAQAnswer]:
        """Search for FAQ answers to the generated questions"""
        faq_answers = []

        for question in questions:
            try:
                # Generate embedding for the question
                question_vector = self.embedding_model.embed_query(question.question)

                # Search in Qdrant
                search_results = self.client.search_points(
                    collection_name=self.collection_name,
                    query_vector=question_vector,
                    limit=3,
                    score_threshold=0.6
                )

                # Process results
                if 'result' in search_results:
                    points = search_results['result']
                    for point in points:
                        payload = point.get('payload', {})
                        content = payload.get('content', '')
                        score = point.get('score', 0)
                        filename = payload.get('filename', '')

                        if content.strip():
                            faq_answers.append(FAQAnswer(
                                question=question.question,
                                answer=content,
                                score=score,
                                source_document=filename
                            ))
                            break  # Take the best answer for each question

            except Exception as e:
                logger.error(f"Error searching FAQ for question '{question.question}': {e}")
                continue

        return faq_answers

class EnhancedUserStoryGenerator:
    """Generates user stories using document content + FAQ answers"""

    def __init__(self):
        if not model:
            raise ValueError("LLM model not available")
        self.model = model

        # Enhanced prompt for user story generation
        self.story_prompt = """
You are an expert Business Analyst tasked with generating comprehensive Agile Scrum User Stories from a Business Requirements Document (BRD) and related FAQ answers.

You have been provided with:
1. The complete BRD content
2. FAQ answers that provide additional context and clarifications

Your task is to generate a JIRA epic and a comprehensive list of Agile Scrum User Stories that cover all the requirements mentioned in both the BRD and the FAQ answers.

Important guidelines:
- Analyze both the BRD content and FAQ answers to understand the complete scope
- Generate user stories that are INVEST compliant (Independent, Negotiable, Valuable, Estimable, Small, Testable)
- Include acceptance criteria in Given/When/Then format
- Assign appropriate priority levels (High/Medium/Low)
- Assign story points using Fibonacci scale (1, 2, 3, 5, 8, 13)
- Focus on user value and business outcomes
- Avoid technical implementation details

Structure for each story:
1. Title (clear and concise)
2. Description (As a [user], I want [goal] so that [benefit])
3. Acceptance Criteria (Given/When/Then format)
4. Priority (High/Medium/Low)
5. Story Points (1, 2, 3, 5, 8, 13)

BRD Content:
{brd_content}

FAQ Answers and Additional Context:
{faq_context}

Generate a comprehensive epic with user stories that address all the requirements and clarifications provided.
"""

    def generate_user_stories(self, document_text: str, faq_answers: List[FAQAnswer]) -> JiraEpic:
        """Generate user stories from document and FAQ answers"""

        # Prepare FAQ context
        faq_context = "\n\n".join([
            f"Q: {faq.question}\nA: {faq.answer}\nSource: {faq.source_document}\nRelevance Score: {faq.score:.2f}"
            for faq in faq_answers
        ])

        # Combine contexts
        full_context = self.story_prompt.format(
            brd_content=document_text[:6000],  # Limit BRD content
            faq_context=faq_context[:4000]     # Limit FAQ content
        )

        try:
            # Use structured output for consistent format
            structured_llm = self.model.with_structured_output(JiraEpic)
            epic = structured_llm.invoke(full_context)
            return epic

        except Exception as e:
            logger.error(f"Error generating user stories: {e}")
            # Return a fallback epic
            return JiraEpic(
                title="BRD Analysis Epic",
                description="Epic generated from BRD analysis with FAQ integration",
                issues=[
                    JiraIssue(
                        title="Analyze Requirements",
                        description="As a business analyst, I want to analyze the BRD requirements so that I can understand the project scope",
                        acceptance_criteria="Given the BRD is provided, When I analyze the content, Then I can identify key requirements",
                        priority="High",
                        story_points=5
                    )
                ]
            )

class QdrantRetriever:
    def __init__(self,
                 client: QdrantHTTPClient,
                 collection_name: str):
        """
        Initialize Qdrant retriever using HTTP client

        Args:
            client: QdrantHTTPClient instance
            collection_name: Name of the collection in Qdrant
        """
        self.client = client
        self.collection_name = collection_name

    def search_relevant_chunks(
        self,
        query: str,
        limit: int = 10,
        score_threshold: float = 0.7,
        document_filter: Optional[str] = None
    ) -> List[dict]:
        try:
            print(f"Generating embedding for query: {query}")
            if not embedding_llm:
                raise ValueError("Embedding model not available")

            query_vector = embedding_llm.embed_query(query)

            print(f"Searching in collection '{self.collection_name}' for query: {query}")

            search_results = self.client.search_points(
                collection_name=self.collection_name,
                query_vector=query_vector,
                limit=limit,
                score_threshold=score_threshold
            )

            formatted_results = []
            if 'result' in search_results:
                points = search_results['result']
            else:
                points = search_results.get('points', [])

            for point in points:
                try:
                    payload = point.get("payload", {})

                    if document_filter and payload.get('filename') != document_filter:
                        continue

                    content = payload.get("content", "")
                    metadata = payload.get("metadata", {})

                except (json.JSONDecodeError, KeyError):
                    content = payload.get("content", "")
                    metadata = payload.get("metadata", {})

                formatted_results.append({
                    'content': content,
                    'metadata': metadata,
                    'score': point.get('score', 0),
                    'filename': payload.get('filename', ''),
                    'document_id': payload.get('document_id', ''),
                    'document_type': payload.get('document_type', ''),
                    'chunk_index': payload.get('chunk_index', ''),
                    'chunk_id': str(point.get('id', ''))
                })

            print(f"Found {len(formatted_results)} relevant chunks")
            return formatted_results

        except Exception as e:
            logger.error(f"Error in search: {e}")
            return []

    def get_all_document_content(self, document_name: Optional[str] = None) -> str:
        """Get all content from a document using scroll (no vector search needed)"""
        try:
            print(f"Retrieving all content for document: {document_name or 'all documents'}")

            scroll_results = self.client.scroll_points(
                collection_name=self.collection_name,
                limit=1000,
                with_payload=True
            )

            print(f"Scroll results: {scroll_results}")

            chunks = []
            if 'result' in scroll_results:
                points = scroll_results['result'].get('points', [])
                for point in points:
                    payload = point.get('payload', {})

                    if document_name and payload.get('filename') != document_name:
                        continue

                    content = payload.get("content", "")

                    if content.strip():
                        chunks.append({
                            'content': content,
                            'filename': payload.get('filename', ''),
                            'document_id': payload.get('document_id', ''),
                            'document_type': payload.get('document_type', ''),
                            'chunk_index': payload.get('chunk_index', ''),
                            'metadata': payload.get('metadata', {})
                        })

            print(f"Found {len(chunks)} content chunks")
            combined_content = [chunk['content'] for chunk in chunks]
            return "\n\n".join(combined_content)

        except Exception as e:
            logger.error(f"Error getting all content: {e}")
            return ""

    def get_context_by_query(
        self,
        query: str,
        document_name: Optional[str] = None,
        limit: int = 20,
        score_threshold: float = 0.5
    ) -> str:
        """Get context based on a specific user query"""
        print(f"Retrieving context for query: '{query}'")

        chunks = self.search_relevant_chunks(
            query=query,
            document_filter=document_name,
            limit=limit,
            score_threshold=score_threshold
        )

        if not chunks:
            print("No chunks found for query, falling back to all document content")
            return self.get_all_document_content(document_name)

        chunks.sort(key=lambda x: x.get('score', 0), reverse=True)
        combined_content = [chunk['content'] for chunk in chunks]
        return "\n\n".join(combined_content)

    def get_available_documents(self) -> List[str]:
        """Get list of all available documents in the collection"""
        try:
            scroll_results = self.client.scroll_points(
                collection_name=self.collection_name,
                limit=1000,
                with_payload=True
            )

            documents = set()
            if 'result' in scroll_results:
                points = scroll_results['result'].get('points', [])
                for point in points:
                    payload = point.get('payload', {})
                    filename = payload.get('filename', '')
                    if filename:
                        documents.add(filename)

            return sorted(list(documents))
        except Exception as e:
            logger.error(f"Error getting documents: {e}")
            return []

def initialize_qdrant_http_client(url: str = None, api_key: str = None) -> QdrantHTTPClient:
    """Initialize Qdrant HTTP client"""
    qdrant_url = url or os.getenv("QDRANT_ENDPOINT")
    qdrant_api_key = api_key or os.getenv("QDRANT_API_KEY")

    if not qdrant_url:
        raise ValueError("QDRANT_ENDPOINT environment variable is not set")
    if not qdrant_api_key:
        raise ValueError("QDRANT_API_KEY environment variable is not set")

    print(f"Connecting to Qdrant at: {qdrant_url}")
    return QdrantHTTPClient(
        url=qdrant_url,
        api_key=qdrant_api_key,
        timeout=60
    )

def test_qdrant_connection(client: QdrantHTTPClient, collection_name: str) -> bool:
    """Test Qdrant connection and collection availability"""
    try:
        collections = client.get_collections()
        collection_names = [c['name'] for c in collections.get('result', {}).get('collections', [])]
        print(f"Available collections: {collection_names}")

        if collection_name not in collection_names:
            raise ValueError(f"Collection '{collection_name}' not found in Qdrant")

        collection_info = client.get_collection_info(collection_name)
        points_count = collection_info.get('result', {}).get('points_count', 0)
        print(f"Collection '{collection_name}' found with {points_count} points")
        return True

    except Exception as e:
        print(f"Qdrant connection test failed: {e}")
        return False

BA_PROMPT = """
You are a Business Analyst tasked with generating Agile Scrum User Stories from Business Requirement Document (BRD) or Product Requirement Document (PRD) content.

The BRD/PRD content below contains business requirements retrieved from a vector database based on relevance.
Generate a JIRA epic and a list of Agile Scrum User Stories in that Epic following the structure below and return it as a JSON.

Important guidelines:
- Don't return AcceptanceCriteria as a JSON Array - it should be represented as a single field even if it is long multiline text.
- Ensure the JSON is not malformed in the response
- Use the provided context to understand the full scope of requirements

Structure for each story:
1. Title
2. Description (As a..., I want..., so that...)
3. AcceptanceCriteria (Given / When / Then)
4. Priority (High/Medium/Low)
5. StoryPoints (Fibonacci scale: 1, 2, 3, 5, 8, 13)

Guidelines:
- Follow the INVEST principle (Independent, Negotiable, Valuable, Estimable, Small, Testable)
- Avoid technical or implementation specifics
- Extract only actionable, testable features and behaviors
- Consider the context is for a Scrum team working in 2-week sprints
- The product feature set is intended to enhance user experience for Container Depots

Please analyze the context and generate complete, well-structured stories.
"""

if model:
    structured_llm = model.with_structured_output(JiraEpic)
    prompt_template = ChatPromptTemplate.from_messages([
        ("system", BA_PROMPT),
        ("human", "BRD/PRD Context from Vector Store:\n\n{content}"),
    ])
    BA_agent = prompt_template | structured_llm
else:
    structured_llm = None
    prompt_template = None
    BA_agent = None

def create_tasks_from_vector_store(state: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create tasks using context retrieved from Qdrant vector store using HTTP client

    Args:
        state: Dictionary containing:
            - qdrant_client: QdrantHTTPClient instance
            - collection_name: Name of the collection
    """
    print("------------------------BA Agent Vector Store Search-------------------------")

    qdrant_client = state.get("qdrant_client")
    collection_name = state.get("collection_name")

    if not qdrant_client:
        raise ValueError("No qdrant_client provided in state.")

    if not collection_name:
        raise ValueError("No collection_name provided in state.")

    if not BA_agent:
        raise ValueError("BA agent not initialized - check model configuration")

    retriever = QdrantRetriever(
        client=qdrant_client,
        collection_name=collection_name
    )

    if user_query:
        print(f"Using user query: '{user_query}' for document: {document_name or 'all documents'}")
        context = retriever.get_context_by_query(
            query=user_query,
            document_name=document_name,
            limit=20,
            score_threshold=0.5
        )
    else:
        print(f"No specific query provided, retrieving all content for: {document_name or 'all documents'}")
        context = retriever.get_all_document_content(document_name=document_name)

    if not context:
        target = document_name or "the collection"
        raise ValueError(f"No relevant context found for: {target}")

    logger.info(f"Retrieved context length: {len(context)} characters")

    # Generate user stories from the context
    epic = BA_agent.invoke({"content": context})

    return {
        "epic": epic,
        "context_length": len(context),
        "query_used": user_query,
        "document_name": document_name,
        "retrieval_method": "query-based" if user_query else "full-document"
    }

# FastAPI Endpoints
@app.get("/")
async def root():
    return {
        "message": "Enhanced BA Agent API is running",
        "status": "healthy",
        "version": "2.0.0",
        "features": [
            "Document upload and processing",
            "Question generation from BRD",
            "FAQ search and integration",
            "Enhanced user story generation"
        ]
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "model_available": model is not None,
        "embedding_model_available": embedding_llm is not None,
        "qdrant_connection": test_qdrant_connection(
            initialize_qdrant_http_client(),
            os.getenv('QDRANT_BRD_COLLECTION_NAME', 'BRD_collection')
        ) if model and embedding_llm else False
    }

@app.post("/upload-document", response_model=DocumentUploadResponse)
async def upload_document(file: UploadFile = File(...)):
    """Upload and process a DOCX document"""
    try:
        # Validate file type
        if not file.filename.endswith('.docx'):
            raise HTTPException(status_code=400, detail="Only DOCX files are supported")

        # Generate unique document ID
        import uuid
        document_id = str(uuid.uuid4())

        # Save uploaded file temporarily
        import tempfile
        with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name

        try:
            # Extract text from DOCX
            document_text = DocumentProcessor.extract_text_from_docx(temp_file_path)

            # Store processed document
            processed_documents[document_id] = {
                "filename": file.filename,
                "text": document_text,
                "upload_time": json.dumps({"timestamp": "now"}),  # Simplified timestamp
                "status": "processed"
            }

            return DocumentUploadResponse(
                success=True,
                message="Document uploaded and processed successfully",
                document_id=document_id,
                filename=file.filename,
                text_length=len(document_text)
            )

        finally:
            # Clean up temp file
            os.unlink(temp_file_path)

    except Exception as e:
        logger.error(f"Error uploading document: {e}")
        return DocumentUploadResponse(
            success=False,
            message="Failed to process document",
            error=str(e)
        )

@app.post("/generate-questions/{document_id}", response_model=QuestionGenerationResponse)
async def generate_questions(document_id: str):
    """Generate questions from uploaded document"""
    try:
        # Check if document exists
        if document_id not in processed_documents:
            raise HTTPException(status_code=404, detail="Document not found")

        document_data = processed_documents[document_id]
        document_text = document_data["text"]

        # Generate questions
        question_generator = QuestionGenerator()
        questions = question_generator.generate_questions(document_text)

        # Store questions in document data
        processed_documents[document_id]["questions"] = [q.model_dump() for q in questions]

        return QuestionGenerationResponse(
            success=True,
            document_id=document_id,
            questions=questions,
            questions_count=len(questions)
        )

    except Exception as e:
        logger.error(f"Error generating questions: {e}")
        return QuestionGenerationResponse(
            success=False,
            document_id=document_id,
            questions=[],
            questions_count=0,
            error=str(e)
        )

@app.post("/search-faq/{document_id}", response_model=FAQSearchResponse)
async def search_faq_answers(document_id: str, request: ProcessBRDRequest):
    """Search FAQ database for answers to generated questions"""
    try:
        # Check if document exists and has questions
        if document_id not in processed_documents:
            raise HTTPException(status_code=404, detail="Document not found")

        document_data = processed_documents[document_id]
        if "questions" not in document_data:
            raise HTTPException(status_code=400, detail="No questions generated for this document. Run /generate-questions first.")

        # Initialize Qdrant client
        qdrant_client = initialize_qdrant_http_client()
        collection_name = request.collection_name or os.getenv('QDRANT_BRD_COLLECTION_NAME')

        if not collection_name:
            raise HTTPException(status_code=400, detail="Collection name not provided and QDRANT_BRD_COLLECTION_NAME not set")

        if not test_qdrant_connection(qdrant_client, collection_name):
            raise HTTPException(status_code=503, detail="Cannot connect to Qdrant database")

        # Convert stored questions back to objects
        questions = [GeneratedQuestion(**q) for q in document_data["questions"]]

        # Search for FAQ answers
        faq_searcher = FAQSearcher(qdrant_client, collection_name)
        faq_answers = faq_searcher.search_faq_answers(questions)

        # Store FAQ answers in document data
        processed_documents[document_id]["faq_answers"] = [
            {
                "question": faq.question,
                "answer": faq.answer,
                "score": faq.score,
                "source_document": faq.source_document
            }
            for faq in faq_answers
        ]

        return FAQSearchResponse(
            success=True,
            document_id=document_id,
            faq_answers=faq_answers,
            answers_found=len(faq_answers)
        )

    except Exception as e:
        logger.error(f"Error searching FAQ: {e}")
        return FAQSearchResponse(
            success=False,
            document_id=document_id,
            faq_answers=[],
            answers_found=0,
            error=str(e)
        )

@app.post("/generate-user-stories/{document_id}", response_model=UserStoryGenerationResponse)
async def generate_user_stories(document_id: str):
    """Generate user stories from document + FAQ answers"""
    try:
        # Check if document exists with all required data
        if document_id not in processed_documents:
            raise HTTPException(status_code=404, detail="Document not found")

        document_data = processed_documents[document_id]

        if "text" not in document_data:
            raise HTTPException(status_code=400, detail="Document text not available")

        if "faq_answers" not in document_data:
            raise HTTPException(status_code=400, detail="FAQ answers not available. Run /search-faq first.")

        # Get document text and FAQ answers
        document_text = document_data["text"]
        faq_data = document_data["faq_answers"]

        # Convert FAQ data back to objects
        faq_answers = [
            FAQAnswer(
                question=faq["question"],
                answer=faq["answer"],
                score=faq["score"],
                source_document=faq.get("source_document")
            )
            for faq in faq_data
        ]

        # Generate user stories
        story_generator = EnhancedUserStoryGenerator()
        epic = story_generator.generate_user_stories(document_text, faq_answers)

        # Store the generated epic
        processed_documents[document_id]["epic"] = epic.model_dump()

        # Prepare context summary
        context_summary = f"Document: {len(document_text)} chars, FAQ Answers: {len(faq_answers)} items"

        return UserStoryGenerationResponse(
            success=True,
            document_id=document_id,
            epic=epic,
            context_used=context_summary,
            questions_count=len(document_data.get("questions", [])),
            faq_answers_count=len(faq_answers)
        )

    except Exception as e:
        logger.error(f"Error generating user stories: {e}")
        return UserStoryGenerationResponse(
            success=False,
            document_id=document_id,
            error=str(e)
        )

@app.post("/process-brd-complete", response_model=UserStoryGenerationResponse)
async def process_brd_complete(file: UploadFile = File(...), collection_name: Optional[str] = None):
    """Complete BRD processing workflow: upload -> questions -> FAQ search -> user stories"""
    try:
        # Step 1: Upload and process document
        upload_response = await upload_document(file)
        if not upload_response.success:
            raise HTTPException(status_code=400, detail=f"Upload failed: {upload_response.error}")

        document_id = upload_response.document_id

        # Step 2: Generate questions
        questions_response = await generate_questions(document_id)
        if not questions_response.success:
            raise HTTPException(status_code=400, detail=f"Question generation failed: {questions_response.error}")

        # Step 3: Search FAQ
        faq_request = ProcessBRDRequest(document_id=document_id, collection_name=collection_name)
        faq_response = await search_faq_answers(document_id, faq_request)
        if not faq_response.success:
            raise HTTPException(status_code=400, detail=f"FAQ search failed: {faq_response.error}")

        # Step 4: Generate user stories
        stories_response = await generate_user_stories(document_id)
        if not stories_response.success:
            raise HTTPException(status_code=400, detail=f"User story generation failed: {stories_response.error}")

        return stories_response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in complete BRD processing: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/processed-documents")
async def list_processed_documents():
    """List all processed documents"""
    return {
        "documents": [
            {
                "document_id": doc_id,
                "filename": data.get("filename"),
                "status": data.get("status"),
                "has_questions": "questions" in data,
                "has_faq_answers": "faq_answers" in data,
                "has_epic": "epic" in data,
                "text_length": len(data.get("text", ""))
            }
            for doc_id, data in processed_documents.items()
        ],
        "total_count": len(processed_documents)
    }

@app.get("/document/{document_id}")
async def get_document_details(document_id: str):
    """Get details of a processed document"""
    if document_id not in processed_documents:
        raise HTTPException(status_code=404, detail="Document not found")

    data = processed_documents[document_id]
    return {
        "document_id": document_id,
        "filename": data.get("filename"),
        "status": data.get("status"),
        "text_length": len(data.get("text", "")),
        "questions_count": len(data.get("questions", [])),
        "faq_answers_count": len(data.get("faq_answers", [])),
        "has_epic": "epic" in data,
        "text_preview": data.get("text", "")[:500] + "..." if len(data.get("text", "")) > 500 else data.get("text", "")
    }

@app.get("/collections")
async def list_collections():
    """List available collections in Qdrant"""
    try:
        qdrant_client = initialize_qdrant_http_client()
        collections = qdrant_client.get_collections()
        return collections
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Cannot connect to Qdrant: {e}")

def main():
    """Main function for testing"""
    print("Starting BA Agent...")
    print("Initializing Qdrant HTTP client")
    print(f"Qdrant endpoint: {os.getenv('QDRANT_ENDPOINT')}")

    try:
        qdrant_client = initialize_qdrant_http_client()
        collection_name = os.getenv('QDRANT_BRD_COLLECTION_NAME')

        if not collection_name:
            raise ValueError("QDRANT_BRD_COLLECTION_NAME environment variable is not set")

        if not test_qdrant_connection(qdrant_client, collection_name):
            print("Cannot connect to Qdrant")
            return

        state = {
            "qdrant_client": qdrant_client,
            "collection_name": collection_name
        }

        result = create_tasks_from_vector_store(state)
        epic = result["epic"]

        md_txt = f"""
Epic: {epic.title}
Description: {epic.description}
"""
        print(md_txt)

        for n, issue in enumerate(epic.issues, start=1):
            issue_txt = f"""
Story {n}: {issue.title}
Description: {issue.description}
Acceptance Criteria: {issue.acceptance_criteria}
Priority: {issue.priority}
Story Points: {issue.story_points}
"""
            print(issue_txt)
            print("-" * 40)
            md_txt += f"""------------------------------\n{issue_txt}"""

        with open("stories_from_vector_store.md", "w") as f:
            f.write(md_txt)

        print("Stories generated successfully from vector store!")

    except ValueError as e:
        print(f"Configuration Error: {e}")
        print("Please check your environment variables and try again.")
    except Exception as e:
        print(f" Error generating stories: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "server":
        uvicorn.run(app, host="0.0.0.0", port=8000)
    else:
        
        main()