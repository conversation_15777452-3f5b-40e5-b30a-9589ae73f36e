"""
Enhanced BA Agent API
Single endpoint for complete BRD processing workflow:
Upload DOCX → Extract Text → Generate Questions → Search FAQ → Generate User Stories
"""

import os
import logging
import uvicorn
from typing import List, Optional

from fastapi import FastAPI, HTTPException, UploadFile, File
from pydantic import BaseModel, Field
from dotenv import load_dotenv

from brd_processor import BRDProcessor
from question_generator import QuestionGenerator, GeneratedQuestion
from faq_searcher import <PERSON>QSearcher, FA<PERSON><PERSON><PERSON>wer
from story_generator import UserStoryGenerator, <PERSON>raEpic, JiraIssue
from qdrant_http_client import QdrantHTTPClient

load_dotenv()

try:
    from langchain_openai import AzureChatOpenAI, AzureOpenAIEmbeddings

    model = AzureChatOpenAI(
        azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT_4_1"),
        api_key=os.getenv("AZURE_OPENAI_API_KEY_4_1"),
        api_version=os.getenv("AZURE_OPENAI_VERSION_4_1"),
        deployment_name="gpt-4o",
        temperature=0.1,
        max_tokens=4000  
    )

    embedding_llm = AzureOpenAIEmbeddings(
        azure_endpoint=os.getenv("EMBEDDING_OPENAI_API_BASE"),
        api_key=os.getenv("EMBEDDING_OPENAI_API_KEY"),
        api_version=os.getenv("EMBEDDING_OPENAI_API_VERSION"),
        deployment="text-embedding-3-large"
    )

except ImportError as e:
    print(f"Warning: Could not import LangChain modules: {e}")
    model = None
    embedding_llm = None

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Enhanced BA Agent API", 
    description="Single endpoint BRD processing: Upload → Questions → FAQ Search → User Stories",
    version="2.0.0"
)

class BRDProcessResponse(BaseModel):
    success: bool
    filename: str
    text_length: int
    questions_generated: int
    faq_answers_found: int
    epic: Optional[JiraEpic] = None
    processing_steps: List[str]
    error: Optional[str] = None

# Helper functions
def initialize_qdrant_http_client() -> QdrantHTTPClient:
    """Initialize Qdrant HTTP client"""
    qdrant_url = os.getenv("QDRANT_ENDPOINT")
    qdrant_api_key = os.getenv("QDRANT_API_KEY")
    
    if not qdrant_url:
        raise ValueError("QDRANT_ENDPOINT environment variable is not set")
    if not qdrant_api_key:
        raise ValueError("QDRANT_API_KEY environment variable is not set")

    return QdrantHTTPClient(
        url=qdrant_url,
        api_key=qdrant_api_key,
        timeout=60
    )

def test_qdrant_connection(client: QdrantHTTPClient, collection_name: str) -> bool:
    """Test Qdrant connection and collection availability"""
    try:
        collections = client.get_collections()
        collection_names = [c['name'] for c in collections.get('result', {}).get('collections', [])]
        
        if collection_name not in collection_names:
            logger.error(f"Collection '{collection_name}' not found in Qdrant")
            return False
            
        return True
        
    except Exception as e:
        logger.error(f"Qdrant connection test failed: {e}")
        return False

# Main endpoint
@app.post("/process-brd", response_model=BRDProcessResponse)
async def process_brd_complete(
    file: UploadFile = File(...), 
    collection_name: Optional[str] = None
):
    """
    Complete BRD processing workflow in one endpoint:
    1. Upload and extract text from DOCX
    2. Generate questions from document content
    3. Search FAQ database for answers
    4. Generate user stories from document + FAQ context
    """
    processing_steps = []
    
    try:
        # Step 1: Validate and process document
        processing_steps.append("Validating DOCX file")
        if not BRDProcessor.validate_docx_file(file.filename):
            raise HTTPException(status_code=400, detail="Only DOCX files are supported")
        
        processing_steps.append("Extracting text from DOCX")
        
        # Save uploaded file temporarily
        file_content = await file.read()
        temp_file_path = BRDProcessor.save_temp_file(file_content)
        
        try:
            # Extract text from DOCX
            document_text = BRDProcessor.extract_text_from_docx(temp_file_path)
            
            if not document_text.strip():
                raise ValueError("No text content found in the document")
            
            processing_steps.append(f"Extracted {len(document_text)} characters")
            
            # Step 2: Generate questions
            processing_steps.append("Generating questions from document")
            
            if not model:
                raise ValueError("LLM model not available - check Azure OpenAI configuration")
            
            question_generator = QuestionGenerator(model)
            questions = question_generator.generate_questions(document_text)
            
            processing_steps.append(f"Generated {len(questions)} questions")
            
            # Step 3: Search FAQ database
            processing_steps.append("Searching FAQ database for answers")
            
            # Initialize Qdrant client
            qdrant_client = initialize_qdrant_http_client()
            faq_collection = os.getenv('QDRANT_BRD_COLLECTION_NAME')
            
            if not faq_collection:
                raise ValueError("QDRANT_BRD_COLLECTION_NAME not set")
            
            if not test_qdrant_connection(qdrant_client, faq_collection):
                raise ValueError("Cannot connect to Qdrant database or collection not found")
            
            if not embedding_llm:
                raise ValueError("Embedding model not available - check Azure OpenAI configuration")
            
            faq_searcher = FAQSearcher(qdrant_client, faq_collection, embedding_llm)
            faq_answers = faq_searcher.search_faq_answers(questions)
            
            processing_steps.append(f"Found {len(faq_answers)} FAQ answers")
            
            # Step 4: Generate user stories
            processing_steps.append("Generating user stories from combined context")
            
            story_generator = UserStoryGenerator(model)
            epic = story_generator.generate_user_stories(document_text, faq_answers)
            
            processing_steps.append(f"Generated epic with {len(epic.issues)} user stories")
            
            return BRDProcessResponse(
                success=True,
                filename=file.filename,
                text_length=len(document_text),
                questions_generated=len(questions),
                faq_answers_found=len(faq_answers),
                epic=epic,
                processing_steps=processing_steps
            )
            
        finally:
            # Clean up temp file
            BRDProcessor.cleanup_temp_file(temp_file_path)
            
    except ValueError as e:
        processing_steps.append(f"Error: {str(e)}")
        return BRDProcessResponse(
            success=False,
            filename=file.filename,
            text_length=0,
            questions_generated=0,
            faq_answers_found=0,
            processing_steps=processing_steps,
            error=str(e)
        )
    except Exception as e:
        processing_steps.append(f"Unexpected error: {str(e)}")
        logger.error(f"Error processing BRD: {e}")
        return BRDProcessResponse(
            success=False,
            filename=file.filename,
            text_length=0,
            questions_generated=0,
            faq_answers_found=0,
            processing_steps=processing_steps,
            error=str(e)
        )

# Health check endpoints
@app.get("/")
async def root():
    return {
        "message": "Enhanced BA Agent API is running", 
        "status": "healthy",
        "version": "2.0.0",
        "endpoint": "/process-brd"
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "model_available": model is not None,
        "embedding_model_available": embedding_llm is not None,
        "qdrant_connection": test_qdrant_connection(
            initialize_qdrant_http_client(), 
            os.getenv('QDRANT_BRD_COLLECTION_NAME', 'BRD_collection')
        ) if model and embedding_llm else False
    }

@app.get("/collections")
async def list_collections():
    """List available collections in Qdrant"""
    try:
        qdrant_client = initialize_qdrant_http_client()
        collections = qdrant_client.get_collections()
        return collections
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Cannot connect to Qdrant: {e}")

# Main function
def main():
    """Main function for testing"""
    print("Enhanced BA Agent API - Single Endpoint BRD Processing")
    print("=" * 60)
    print("Endpoint: POST /process-brd")
    print("Upload a DOCX file to get complete user story generation")

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "server":
        print("🚀 Starting Enhanced BA Agent API server...")
        uvicorn.run(app, host="0.0.0.0", port=8000)
    else:
        main()
