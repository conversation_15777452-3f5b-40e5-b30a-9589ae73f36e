#!/usr/bin/env python3
"""
BA Agent API using QdrantHTTPClient
Simple FastAPI server that retrieves context from Qdrant and can be extended with LLM
"""

import os
import sys
import json
import logging
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.append(os.path.dirname(__file__))

try:
    from fastapi import FastAPI, HTTPException
    from fastapi.responses import JSONResponse
    from pydantic import BaseModel, Field
    import uvicorn
    FASTAPI_AVAILABLE = True

    # Pydantic models (only if FastAPI is available)
    class RetrieveContextRequest(BaseModel):
        document_name: Optional[str] = Field(default=None, description="Name of the document to retrieve (optional)")
        limit: Optional[int] = Field(default=1000, description="Maximum number of chunks to retrieve")

    class RetrieveContextResponse(BaseModel):
        success: bool
        document_name: Optional[str] = None
        available_documents: List[str] = []
        context: Optional[str] = None
        context_length: Optional[int] = None
        chunks_count: Optional[int] = None
        error: Optional[str] = None

    class GenerateStoriesRequest(BaseModel):
        document_name: Optional[str] = Field(default=None, description="Name of the document to analyze")
        custom_context: Optional[str] = Field(default=None, description="Custom context instead of retrieving from vector store")

    class GenerateStoriesResponse(BaseModel):
        success: bool
        context_used: Optional[str] = None
        context_length: Optional[int] = None
        generated_stories: Optional[str] = None
        error: Optional[str] = None

except ImportError:
    print("FastAPI not available. Install with: pip install fastapi uvicorn")
    FASTAPI_AVAILABLE = False

    # Dummy classes for when FastAPI is not available
    class RetrieveContextRequest: pass
    class RetrieveContextResponse: pass
    class GenerateStoriesRequest: pass
    class GenerateStoriesResponse: pass

from qdrant_http_client import QdrantHTTPClient

logger = logging.getLogger(__name__)

# FastAPI app
if FASTAPI_AVAILABLE:
    app = FastAPI(
        title="BA Agent API", 
        description="Business Analyst Agent for retrieving context and generating user stories",
        version="1.0.0"
    )

class QdrantContextRetriever:
    """Context retriever using QdrantHTTPClient"""
    
    def __init__(self):
        self.client = self._initialize_client()
        self.collection_name = os.getenv('QDRANT_BRD_COLLECTION_NAME', 'BRD_collection')
    
    def _initialize_client(self) -> QdrantHTTPClient:
        """Initialize Qdrant HTTP client"""
        qdrant_url = os.getenv("QDRANT_ENDPOINT")
        qdrant_api_key = os.getenv("QDRANT_API_KEY")
        
        if not qdrant_url:
            raise ValueError("QDRANT_ENDPOINT environment variable is not set")
        if not qdrant_api_key:
            raise ValueError("QDRANT_API_KEY environment variable is not set")

        return QdrantHTTPClient(
            url=qdrant_url,
            api_key=qdrant_api_key,
            timeout=60
        )
    
    def test_connection(self) -> bool:
        """Test Qdrant connection"""
        try:
            collections = self.client.get_collections()
            collection_names = [c['name'] for c in collections.get('result', {}).get('collections', [])]
            
            if self.collection_name not in collection_names:
                logger.error(f"Collection '{self.collection_name}' not found")
                return False
                
            return True
        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            return False
    
    def get_available_documents(self) -> List[str]:
        """Get list of all available documents"""
        try:
            scroll_results = self.client.scroll_points(
                collection_name=self.collection_name,
                limit=1000,
                with_payload=True
            )
            
            documents = set()
            if 'result' in scroll_results:
                points = scroll_results['result'].get('points', [])
                for point in points:
                    payload = point.get('payload', {})
                    doc_name = payload.get('document_name', '')
                    if doc_name:
                        documents.add(doc_name)
            
            return sorted(list(documents))
        except Exception as e:
            logger.error(f"Error getting documents: {e}")
            return []
    
    def retrieve_context(self, document_name: Optional[str] = None, limit: int = 1000) -> Dict[str, Any]:
        """Retrieve context from vector store"""
        try:
            scroll_results = self.client.scroll_points(
                collection_name=self.collection_name,
                limit=limit,
                with_payload=True
            )
            
            chunks = []
            if 'result' in scroll_results:
                points = scroll_results['result'].get('points', [])
                for point in points:
                    payload = point.get('payload', {})
                    
                    # Filter by document if specified
                    if document_name and payload.get('document_name') != document_name:
                        continue
                    
                    try:
                        if "_node_content" in payload:
                            node = json.loads(payload["_node_content"])
                            content = node.get("text", "")
                        else:
                            content = payload.get("content", "")
                    except (json.JSONDecodeError, KeyError):
                        content = payload.get("content", "")
                    
                    if content.strip():
                        chunks.append({
                            'content': content,
                            'document_name': payload.get('document_name', ''),
                            'chunk_id': payload.get('chunk_id', str(point.get('id', '')))
                        })
            
            combined_content = "\n\n".join([chunk['content'] for chunk in chunks])
            
            return {
                'context': combined_content,
                'chunks_count': len(chunks),
                'context_length': len(combined_content)
            }
            
        except Exception as e:
            logger.error(f"Error retrieving context: {e}")
            raise

# Global retriever instance
retriever = None

def get_retriever() -> QdrantContextRetriever:
    """Get or create retriever instance"""
    global retriever
    if retriever is None:
        retriever = QdrantContextRetriever()
    return retriever

# FastAPI endpoints
if FASTAPI_AVAILABLE:
    @app.get("/")
    async def root():
        return {"message": "BA Agent API is running", "status": "healthy"}

    @app.get("/health")
    async def health_check():
        try:
            ret = get_retriever()
            connection_ok = ret.test_connection()
            return {
                "status": "healthy" if connection_ok else "unhealthy",
                "qdrant_connection": connection_ok,
                "collection": ret.collection_name
            }
        except Exception as e:
            return {"status": "unhealthy", "error": str(e)}

    @app.get("/documents")
    async def list_documents():
        """List available documents in the vector store"""
        try:
            ret = get_retriever()
            documents = ret.get_available_documents()
            return {"documents": documents, "count": len(documents)}
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    @app.post("/retrieve-context", response_model=RetrieveContextResponse)
    async def retrieve_context_endpoint(request: RetrieveContextRequest):
        """Retrieve context from vector store"""
        try:
            ret = get_retriever()
            
            # Get available documents
            available_docs = ret.get_available_documents()
            
            # Validate document if specified
            document_name = request.document_name
            if document_name and document_name not in available_docs:
                return RetrieveContextResponse(
                    success=False,
                    available_documents=available_docs,
                    error=f"Document '{document_name}' not found. Available: {available_docs}"
                )
            
            # Retrieve context
            result = ret.retrieve_context(document_name, request.limit)
            
            return RetrieveContextResponse(
                success=True,
                document_name=document_name,
                available_documents=available_docs,
                context=result['context'],
                context_length=result['context_length'],
                chunks_count=result['chunks_count']
            )
            
        except Exception as e:
            return RetrieveContextResponse(
                success=False,
                error=str(e)
            )

    @app.post("/generate-stories", response_model=GenerateStoriesResponse)
    async def generate_stories_endpoint(request: GenerateStoriesRequest):
        """Generate user stories from context (placeholder for LLM integration)"""
        try:
            # Get context
            if request.custom_context:
                context = request.custom_context
                context_length = len(context)
            else:
                ret = get_retriever()
                result = ret.retrieve_context(request.document_name)
                context = result['context']
                context_length = result['context_length']
            
            # Placeholder for LLM processing
            # TODO: Integrate with LangChain/OpenAI to generate actual stories
            generated_stories = f"""
# Generated User Stories (Placeholder)

Based on the retrieved context ({context_length} characters), here would be the generated user stories:

## Epic: Container Depot Management Enhancement

### Story 1: Real-time Container Tracking
**As a** depot manager  
**I want** to track container locations in real-time  
**So that** I can efficiently manage container movements

**Acceptance Criteria:**
- Given I am logged into the system
- When I search for a container
- Then I can see its current location within 5 minutes accuracy

### Story 2: Automated Reporting
**As an** operations manager  
**I want** automated daily reports  
**So that** I can make informed decisions

**Acceptance Criteria:**
- Given it's 6 AM daily
- When the system generates reports
- Then reports are available in my dashboard

*Note: This is a placeholder. Integrate with LLM for actual story generation.*
"""
            
            return GenerateStoriesResponse(
                success=True,
                context_used=context[:500] + "..." if len(context) > 500 else context,
                context_length=context_length,
                generated_stories=generated_stories
            )
            
        except Exception as e:
            return GenerateStoriesResponse(
                success=False,
                error=str(e)
            )

def main():
    """Main function for testing"""
    print("BA Agent API - Testing Context Retrieval")
    print("=" * 50)
    
    try:
        ret = get_retriever()
        
        # Test connection
        if not ret.test_connection():
            print("❌ Cannot connect to Qdrant")
            return
        
        print("✅ Connected to Qdrant")
        
        # Get available documents
        docs = ret.get_available_documents()
        print(f"📄 Available documents: {docs}")
        
        # Retrieve context
        result = ret.retrieve_context(limit=10)  # Limit for testing
        print(f"📊 Retrieved {result['chunks_count']} chunks, {result['context_length']} characters")
        
        # Show preview
        preview = result['context'][:300] + "..." if len(result['context']) > 300 else result['context']
        print(f"\n📖 Context Preview:\n{preview}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "server":
        if FASTAPI_AVAILABLE:
            print("🚀 Starting BA Agent API server...")
            uvicorn.run(app, host="0.0.0.0", port=8000)
        else:
            print("❌ FastAPI not available. Install with: pip install fastapi uvicorn")
    else:
        main()
