"""
Configuration settings for Jarvis Knowledge
"""

import os
from typing import Optional

class Settings:
    """Application settings loaded from environment variables"""
    
    # Server Configuration
    HOST: str = os.getenv("HOST", "0.0.0.0")
    PORT: int = int(os.getenv("PORT", 8000))
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "info")
    RELOAD: bool = os.getenv("RELOAD", "false").lower() == "true"
    
    # Azure OpenAI Configuration
    AZURE_OPENAI_ENDPOINT: str = os.getenv("AZURE_OPENAI_ENDPOINT_4_1", "")
    AZURE_OPENAI_API_KEY: str = os.getenv("AZURE_OPENAI_API_KEY_4_1", "")
    AZURE_OPENAI_VERSION: str = os.getenv("AZURE_OPENAI_VERSION_4_1", "2025-01-01-preview")
    AZURE_OPENAI_MODEL: str = os.getenv("AZURE_OPENAI_MODEL", "gpt-4")
    
    # Embedding Configuration
    EMBEDDING_OPENAI_API_BASE: str = os.getenv("EMBEDDING_OPENAI_API_BASE", "")
    EMBEDDING_OPENAI_API_KEY: str = os.getenv("EMBEDDING_OPENAI_API_KEY", "")
    EMBEDDING_OPENAI_API_VERSION: str = os.getenv("EMBEDDING_OPENAI_API_VERSION", "2023-05-15")
    EMBEDDING_MODEL: str = os.getenv("EMBEDDING_MODEL", "text-embedding-3-large")
    
    # Qdrant Configuration
    QDRANT_ENDPOINT: str = os.getenv("QDRANT_ENDPOINT", "")
    QDRANT_API_KEY: str = os.getenv("QDRANT_API_KEY", "")
    QDRANT_BRD_COLLECTION_NAME: str = os.getenv("QDRANT_BRD_COLLECTION_NAME", "faq_collection")
    
    # Processing Configuration
    MAX_FILE_SIZE: int = int(os.getenv("MAX_FILE_SIZE", 10485760))  # 10MB
    CHUNK_SIZE: int = int(os.getenv("CHUNK_SIZE", 1000))
    CHUNK_OVERLAP: int = int(os.getenv("CHUNK_OVERLAP", 200))
    
    # Question Generation Configuration
    MAX_QUESTIONS: int = int(os.getenv("MAX_QUESTIONS", 10))
    QUESTION_TIMEOUT: int = int(os.getenv("QUESTION_TIMEOUT", 30))
    
    # FAQ Search Configuration
    FAQ_SEARCH_LIMIT: int = int(os.getenv("FAQ_SEARCH_LIMIT", 5))
    FAQ_SCORE_THRESHOLD: float = float(os.getenv("FAQ_SCORE_THRESHOLD", 0.7))
    
    @classmethod
    def validate(cls) -> bool:
        """Validate that required settings are present"""
        required_settings = [
            cls.AZURE_OPENAI_ENDPOINT,
            cls.AZURE_OPENAI_API_KEY,
            cls.EMBEDDING_OPENAI_API_BASE,
            cls.EMBEDDING_OPENAI_API_KEY,
            cls.QDRANT_ENDPOINT,
            cls.QDRANT_API_KEY
        ]
        
        missing = [name for name, value in zip([
            "AZURE_OPENAI_ENDPOINT_4_1",
            "AZURE_OPENAI_API_KEY_4_1", 
            "EMBEDDING_OPENAI_API_BASE",
            "EMBEDDING_OPENAI_API_KEY",
            "QDRANT_ENDPOINT",
            "QDRANT_API_KEY"
        ], required_settings) if not value]
        
        if missing:
            print(f"❌ Missing required environment variables: {', '.join(missing)}")
            return False
        
        return True
    
    @classmethod
    def display_config(cls) -> None:
        """Display current configuration (without sensitive data)"""
        print("🔧 Jarvis Knowledge Configuration:")
        print(f"   • Host: {cls.HOST}")
        print(f"   • Port: {cls.PORT}")
        print(f"   • Log Level: {cls.LOG_LEVEL}")
        print(f"   • Azure OpenAI Model: {cls.AZURE_OPENAI_MODEL}")
        print(f"   • Embedding Model: {cls.EMBEDDING_MODEL}")
        print(f"   • Qdrant Collection: {cls.QDRANT_BRD_COLLECTION_NAME}")
        print(f"   • Max File Size: {cls.MAX_FILE_SIZE} bytes")
        print(f"   • Max Questions: {cls.MAX_QUESTIONS}")

# Global settings instance
settings = Settings()
