"""
BRD Processing Module
Simple DOCX text extraction for BRD processing
"""

import os
import logging
from docx import Document
from typing import Optional

logger = logging.getLogger(__name__)

class BRDProcessor:
    """Simple DOCX processor for BRD text extraction"""
    
    @staticmethod
    def extract_text_from_docx(file_path: str) -> str:
        """
        Extract text content from DOCX file
        
        Args:
            file_path: Path to the DOCX file
            
        Returns:
            Extracted text content as string
        """
        try:
            doc = Document(file_path)
            text_content = []
            
            # Extract text from paragraphs
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text.strip())
            
            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        if cell.text.strip():
                            text_content.append(cell.text.strip())
            
            # Join all text with double newlines
            full_text = "\n\n".join(text_content)
            
            logger.info(f"Successfully extracted {len(full_text)} characters from DOCX")
            return full_text
            
        except Exception as e:
            logger.error(f"Error extracting text from DOCX: {e}")
            raise Exception(f"Failed to process DOCX file: {str(e)}")
    
    @staticmethod
    def validate_docx_file(filename: str) -> bool:
        """
        Validate if the uploaded file is a DOCX file
        
        Args:
            filename: Name of the uploaded file
            
        Returns:
            True if valid DOCX file, False otherwise
        """
        return filename.lower().endswith('.docx')
    
    @staticmethod
    def save_temp_file(file_content: bytes, suffix: str = '.docx') -> str:
        """
        Save uploaded file content to a temporary file
        
        Args:
            file_content: Binary content of the uploaded file
            suffix: File extension suffix
            
        Returns:
            Path to the temporary file
        """
        import tempfile
        
        with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as temp_file:
            temp_file.write(file_content)
            return temp_file.name
    
    @staticmethod
    def cleanup_temp_file(file_path: str) -> None:
        """
        Clean up temporary file
        
        Args:
            file_path: Path to the temporary file to delete
        """
        try:
            if os.path.exists(file_path):
                os.unlink(file_path)
                logger.debug(f"Cleaned up temporary file: {file_path}")
        except Exception as e:
            logger.warning(f"Failed to cleanup temp file {file_path}: {e}")
