"""
BRD Processing Module
Simple DOCX text extraction for BRD processing
"""

import os
import logging
from docx import Document
from docx.oxml.text.paragraph import CT_P
from docx.oxml.table import CT_Tbl
from docx.text.paragraph import Paragraph
from docx.table import Table
from typing import Optional

logger = logging.getLogger(__name__)

class BRDProcessor:
    """Enhanced DOCX processor that preserves document structure and flow"""

    @staticmethod
    def get_paragraph_style(paragraph: Paragraph) -> str:
        """
        Get paragraph style tag for structure preservation

        Args:
            paragraph: Paragraph object

        Returns:
            Style tag (h1, h2, h3, etc. or 'p' for normal)
        """
        try:
            style_name = paragraph.style.name.lower()
            if 'heading 1' in style_name or 'title' in style_name:
                return 'h1'
            elif 'heading 2' in style_name:
                return 'h2'
            elif 'heading 3' in style_name:
                return 'h3'
            elif 'heading 4' in style_name:
                return 'h4'
            elif 'heading' in style_name:
                return 'h5'
            else:
                return 'p'
        except:
            return 'p'

    @staticmethod
    def extract_text_from_cell(cell) -> str:
        """
        Extract text from table cell preserving structure

        Args:
            cell: Table cell object

        Returns:
            Cleaned cell text
        """
        try:
            cell_text = cell.text.strip()
            cell_text = ' '.join(cell_text.split())
            return cell_text if cell_text else ""
        except:
            return ""

    @staticmethod
    def extract_text_from_docx(file_path: str) -> str:
        """
        Extract text content from DOCX file preserving document structure and flow

        Args:
            file_path: Path to the DOCX file

        Returns:
            Extracted text content as string with preserved structure
        """
        try:
            doc = Document(file_path)
            text_content = []

            for element in doc.element.body:
                if isinstance(element, CT_P):
                    paragraph = Paragraph(element, doc)
                    text = paragraph.text.strip()

                    if text:
                        tag = BRDProcessor.get_paragraph_style(paragraph)
                        if tag.startswith('h'):
                            text = f"[{tag.upper()}] {text}"

                        text_content.append(text)

                elif isinstance(element, CT_Tbl):
                    table = Table(element, doc)
                    table_text = "[TABLE] "

                    for row in table.rows:
                        row_text = ""
                        for cell in row.cells:
                            cell_text = BRDProcessor.extract_text_from_cell(cell)
                            if cell_text:
                                row_text += cell_text + " | "

                        if row_text.strip():
                            table_text += row_text + " [ROW_END] "

                    if table_text != "[TABLE] ":
                        text_content.append(table_text.strip())

            full_text = "\n\n".join(text_content)

            logger.info(f"Successfully extracted {len(full_text)} characters from DOCX with preserved structure")
            return full_text

        except Exception as e:
            logger.error(f"Error extracting text from DOCX: {e}")
            raise Exception(f"Failed to process DOCX file: {str(e)}")
    
    @staticmethod
    def validate_docx_file(filename: str) -> bool:
        """
        Validate if the uploaded file is a DOCX file
        
        Args:
            filename: Name of the uploaded file
            
        Returns:
            True if valid DOCX file, False otherwise
        """
        return filename.lower().endswith('.docx')
    
    @staticmethod
    def save_temp_file(file_content: bytes, suffix: str = '.docx') -> str:
        """
        Save uploaded file content to a temporary file
        
        Args:
            file_content: Binary content of the uploaded file
            suffix: File extension suffix
            
        Returns:
            Path to the temporary file
        """
        import tempfile
        
        with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as temp_file:
            temp_file.write(file_content)
            return temp_file.name
    
    @staticmethod
    def cleanup_temp_file(file_path: str) -> None:
        """
        Clean up temporary file
        
        Args:
            file_path: Path to the temporary file to delete
        """
        try:
            if os.path.exists(file_path):
                os.unlink(file_path)
                logger.debug(f"Cleaned up temporary file: {file_path}")
        except Exception as e:
            logger.warning(f"Failed to cleanup temp file {file_path}: {e}")
