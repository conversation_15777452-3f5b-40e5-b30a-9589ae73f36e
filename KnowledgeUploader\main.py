#!/usr/bin/env python3
"""
Enhanced BA Agent - Main Entry Point
Single endpoint BRD processing: Upload DOCX → Generate Questions → Search FAQ → Create User Stories
"""

import os
import sys
import uvicorn
from pathlib import Path

# Add current directory to Python path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def main():
    """Main entry point for Enhanced BA Agent"""

    print("🚀 Enhanced BA Agent API")
    print("=" * 40)
    print("📡 Single endpoint BRD processing")
    print("📄 Upload DOCX → Questions → FAQ Search → User Stories")
    print("=" * 40)

    # Get configuration from environment variables
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", 8000))
    reload = os.getenv("RELOAD", "false").lower() == "true"
    log_level = os.getenv("LOG_LEVEL", "info")

    print(f"🌐 Host: {host}")
    print(f"🔌 Port: {port}")
    print(f"🔄 Reload: {reload}")
    print(f"📊 Log Level: {log_level}")
    print()
    print("📚 Available endpoints:")
    print(f"   • POST http://{host}:{port}/process-brd - Main BRD processing")
    print(f"   • GET  http://{host}:{port}/health - Health check")
    print(f"   • GET  http://{host}:{port}/docs - API documentation")
    print()
    print("🔧 Environment variables required:")
    print("   • AZURE_OPENAI_ENDPOINT_4_1")
    print("   • AZURE_OPENAI_API_KEY_4_1")
    print("   • AZURE_OPENAI_VERSION_4_1")
    print("   • EMBEDDING_OPENAI_API_BASE")
    print("   • EMBEDDING_OPENAI_API_KEY")
    print("   • EMBEDDING_OPENAI_API_VERSION")
    print("   • QDRANT_ENDPOINT")
    print("   • QDRANT_API_KEY")
    print("   • QDRANT_BRD_COLLECTION_NAME")
    print()
    print("🚀 Starting server...")
    print("=" * 40)

    try:
        # Import the FastAPI app
        from agents.BA_Agent import app

        # Start the server
        uvicorn.run(
            app,
            host=host,
            port=port,
            reload=reload,
            log_level=log_level,
            access_log=True
        )

    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()