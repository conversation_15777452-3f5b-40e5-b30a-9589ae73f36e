# Jarvis Knowledge - Enhanced BA Agent

🚀 **Intelligent Business Requirements Processing**: Upload DOCX → Generate Questions → Search FAQ → Create User Stories

An advanced AI-powered Business Analyst Agent that transforms Business Requirements Documents (BRDs) into comprehensive user stories with intelligent FAQ integration and container domain expertise.

## ✨ Features

- **🤖 AI-Powered Analysis**: Advanced LLM-based document understanding
- **📄 Single Endpoint Processing**: Complete workflow in one API call
- **🏗️ Modular Architecture**: Clean, maintainable, and extensible design
- **📝 Smart Question Generation**: Context-aware questions from BRD content
- **🔍 Vector-Based FAQ Search**: Semantic search for relevant answers
- **📋 Comprehensive User Stories**: Complete JIRA epics with acceptance criteria
- **🐳 Container Domain Expertise**: Specialized for maritime and logistics operations
- **⚡ High Performance**: Optimized for enterprise-scale processing
- **🔒 Enterprise Ready**: Secure, scalable, and production-ready

## 🎯 Container Domain Specialization

Jarvis Knowledge is specifically designed for container depot and maritime logistics operations:

- **Reefer Operations**: Temperature management and monitoring systems
- **Yard Management**: Container positioning, tracking, and movement optimization
- **Bonded Operations**: Customs compliance and regulatory workflows
- **Stakeholder Coordination**: Multi-party communication and handshake processes
- **System Integrations**: Seamless connection with external logistics systems
- **Equipment Management**: Container, chassis, and equipment lifecycle tracking

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- Azure OpenAI API access
- Qdrant vector database
- Environment configuration

### Installation

1. **Clone the repository**:
   ```bash
   git clone https://github.com/Maersk-Global/jarvis-knowledge.git
   cd jarvis-knowledge
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure environment** (create `.env` file):
   ```env
   # Azure OpenAI Configuration
   AZURE_OPENAI_ENDPOINT_4_1=your_endpoint
   AZURE_OPENAI_API_KEY_4_1=your_api_key
   AZURE_OPENAI_VERSION_4_1=2025-01-01-preview

   # Embedding Configuration
   EMBEDDING_OPENAI_API_BASE=your_embedding_endpoint
   EMBEDDING_OPENAI_API_KEY=your_embedding_api_key
   EMBEDDING_OPENAI_API_VERSION=2023-05-15

   # Qdrant Configuration
   QDRANT_ENDPOINT=your_qdrant_url
   QDRANT_API_KEY=your_qdrant_api_key
   QDRANT_BRD_COLLECTION_NAME=your_faq_collection
   ```

4. **Start Jarvis Knowledge**:
   ```bash
   python main.py
   ```

🎉 **Jarvis Knowledge API available at**: `http://localhost:8000`

## 📡 API Usage

### **Main Processing Endpoint**
```http
POST /process-brd
Content-Type: multipart/form-data
Body: file=your_brd.docx
```

### **Response Format**
```json
{
  "epic": {
    "title": "Container Management System Enhancement",
    "description": "Epic for implementing advanced container depot management features...",
    "issues": [
      {
        "title": "Reefer Temperature Monitoring System",
        "description": "As a depot manager, I want real-time reefer temperature monitoring...",
        "acceptance_criteria": "Given a reefer container is in the yard, When temperature exceeds setpoint...",
        "priority": "High",
        "story_points": 8
      }
    ]
  }
}
```

### **Additional Endpoints**
- `GET /health` - System health and status
- `GET /docs` - Interactive API documentation
- `GET /collections` - Available knowledge collections

## 🏗️ Architecture

### **Intelligent Processing Pipeline**
```
📤 Document Upload
    ↓
🧠 AI Document Analysis (Structure + Content)
    ↓
❓ Smart Question Generation (Domain-Aware)
    ↓
🔍 Vector-Based FAQ Search (Semantic Matching)
    ↓
📝 User Story Generation (Context-Rich)
    ↓
✅ Structured Epic Output
```

### **Modular Components**
```
jarvis-knowledge/
├── main.py                 # Application entry point
├── agents/
│   ├── __init__.py
│   └── ba_agent.py         # Core BA Agent logic
├── utils/
│   ├── __init__.py
│   ├── brd_processor.py    # Document processing
│   ├── question_generator.py # AI question generation
│   ├── faq_searcher.py     # Vector search engine
│   └── story_generator.py  # User story creation
├── config/                 # Configuration management
├── tests/                  # Comprehensive test suite
├── docs/                   # Documentation
└── requirements.txt        # Dependencies
```

## 🧪 Testing

```bash
# Test with sample BRD
curl --request POST \
  --url http://localhost:8000/process-brd \
  --header 'content-type: multipart/form-data' \
  --form 'file=@sample_brd.docx'

# Health check
curl http://localhost:8000/health
```

## 🔧 Configuration

| Variable | Description | Required |
|----------|-------------|----------|
| `AZURE_OPENAI_ENDPOINT_4_1` | Azure OpenAI service endpoint | ✅ |
| `AZURE_OPENAI_API_KEY_4_1` | Azure OpenAI API key | ✅ |
| `QDRANT_ENDPOINT` | Qdrant vector database URL | ✅ |
| `QDRANT_API_KEY` | Qdrant authentication key | ✅ |
| `QDRANT_BRD_COLLECTION_NAME` | FAQ knowledge collection | ✅ |

## 🚀 Deployment

### **Development**
```bash
python main.py
```

### **Production**
```bash
# Configure production settings
export HOST=0.0.0.0
export PORT=8000
export LOG_LEVEL=info

# Start with production configuration
python main.py
```

## 📚 Documentation

- **API Documentation**: `http://localhost:8000/docs`
- **Health Monitoring**: `http://localhost:8000/health`
- **Knowledge Collections**: `http://localhost:8000/collections`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🏢 Maersk Global

Developed by Maersk Global for intelligent business requirements processing and container logistics optimization.

---

**Jarvis Knowledge** - Transforming business requirements into actionable insights with AI-powered intelligence.
