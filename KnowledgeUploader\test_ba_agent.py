#!/usr/bin/env python3
"""
Test script for BA Agent functionality
"""

import os
import sys
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.append(os.path.dirname(__file__))

from qdrant_http_client import QdrantHTTPClient

def test_qdrant_connection():
    """Test basic Qdrant HTTP client functionality"""
    print("Testing Qdrant HTTP Client...")
    
    try:
        # Initialize client
        client = QdrantHTTPClient(
            url=os.getenv("QDRANT_ENDPOINT"),
            api_key=os.getenv("QDRANT_API_KEY"),
            timeout=30
        )
        
        # Test connection
        print(f"Connecting to: {os.getenv('QDRANT_ENDPOINT')}")
        collections = client.get_collections()
        print(f"✅ Connection successful!")
        print(f"Collections: {json.dumps(collections, indent=2)}")
        
        # Test specific collection
        collection_name = os.getenv('QDRANT_BRD_COLLECTION_NAME', 'BRD_collection')
        if client.collection_exists(collection_name):
            info = client.get_collection_info(collection_name)
            print(f"✅ Collection '{collection_name}' exists")
            print(f"Collection info: {json.dumps(info, indent=2)}")
            
            # Test search with dummy vector
            dummy_vector = [0.1] * 3072  # Assuming 3072 dimensions
            search_results = client.search_points(
                collection_name=collection_name,
                query_vector=dummy_vector,
                limit=3,
                score_threshold=0.1
            )
            print(f"✅ Search test successful")
            print(f"Found {len(search_results.get('result', []))} results")
            
        else:
            print(f"❌ Collection '{collection_name}' not found")
            
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_environment_variables():
    """Test if all required environment variables are set"""
    print("\nTesting Environment Variables...")
    
    required_vars = [
        "QDRANT_ENDPOINT",
        "QDRANT_API_KEY", 
        "QDRANT_BRD_COLLECTION_NAME",
        "AZURE_OPENAI_ENDPOINT_4_1",
        "AZURE_OPENAI_API_KEY_4_1",
        "AZURE_OPENAI_VERSION_4_1",
        "EMBEDDING_OPENAI_API_BASE",
        "EMBEDDING_OPENAI_API_KEY",
        "EMBEDDING_OPENAI_API_VERSION"
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {'*' * min(len(value), 10)}...")
        else:
            print(f"❌ {var}: Not set")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n❌ Missing environment variables: {missing_vars}")
        return False
    else:
        print("\n✅ All environment variables are set")
        return True

def main():
    print("BA Agent Test Suite")
    print("=" * 50)
    
    # Test environment variables
    env_ok = test_environment_variables()
    
    # Test Qdrant connection
    qdrant_ok = test_qdrant_connection()
    
    print("\n" + "=" * 50)
    print("Test Results:")
    print(f"Environment Variables: {'✅ PASS' if env_ok else '❌ FAIL'}")
    print(f"Qdrant Connection: {'✅ PASS' if qdrant_ok else '❌ FAIL'}")
    
    if env_ok and qdrant_ok:
        print("\n🎉 All tests passed! BA Agent should work correctly.")
    else:
        print("\n⚠️  Some tests failed. Please check the configuration.")

if __name__ == "__main__":
    main()
